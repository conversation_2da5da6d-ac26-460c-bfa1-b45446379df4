<template>
    <view>
        <cu-custom bgColor="none" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">{{ design.custom_title_em }}
            </view>
        </cu-custom>
        <view class="cu-bar search">
            <view class="search-form round" style="background: #ffffff; box-shadow: 0 4rpx 12rpx rgba(0,129,255,0.1);">
                <text style="color: #0081ff;padding: 0rpx 24rpx;">🔍</text>
                <input @input="get_ser_name" :value="content" type="text" placeholder="输入岗位名称搜搜看 ..." style="color: #333;" />
            </view>
            <view class="action">
                <button @tap="sou" class="cu-btn shadow-blur round" style="color: #ffffff;background: linear-gradient(45deg, #0081ff, #1cbbb4);">搜索</button>
            </view>
        </view>
        <view style="background: rgba(255, 255, 255, 0.8); box-shadow: 0px 6rpx 8rpx rgba(0, 129, 255, 0.1); border-radius:20rpx; margin: 0 20rpx;">
            <ui-tab v-if="current == 'tab1'" @change="handleChange" :tab="tab_list" mark="text-blue" tpl="long" scroll />
        </view>
        <view style="background: rgba(255, 255, 255, 0.8); box-shadow: 0px 6rpx 8rpx rgba(0, 129, 255, 0.1); border-radius:20rpx; margin: 0 20rpx;">
            <ui-tab v-if="current == 'tab2'" @change="handleChangeKey" :tab="my_list" mark="text-blue" tpl="long"
                scroll />
        </view>
        <view class="animation-slide-right" style="background: transparent; padding-top: 20rpx;">
            <view @tap="get_url" :data-id="item.id"
                style="background-color: #fff; border-radius: 15rpx; min-height: 100px; padding: 20rpx; margin: 20rpx; box-shadow: 0 4rpx 20rpx rgba(0,129,255,0.1);"
                v-for="(item, index) in list" :key="index">
                <view class="flex p-xs margin-bottom-sm mb-sm justify-between align-center"
                    style="position: relative">
                    <text class="text-blue text-bold text-xl text-price"
                        style="position: absolute; right: 0; top: 10px; color: #0081ff;">{{ item.job_salary }}</text>
                    <view class="let">
                        <view style="font-size: 32rpx; margin: 10px 10px 10px 10px; font-weight: 600">
                            <image v-if="item.top_time > 0"
                                style="width: 20px; height: 20px; vertical-align: middle; margin-right: 5px"
                                :src="http_root + 'addons/yl_welore/web/static/applet_icon/top_time.png'"></image>
                            <text style="vertical-align: middle">💼 {{ item.job_name }}</text>
                            <view v-if="item.release_type == 0" class="cu-tag bg-red radius sm margin-left-sm"
                                style="vertical-align: middle">🔥 {{ item.release_type_name }}</view>
                            <view v-if="item.release_type == 1" class="cu-tag bg-blue radius sm margin-left-sm"
                                style="vertical-align: middle">✨ {{ item.release_type_name }}</view>
                            <view v-if="item.release_type == 2" class="cu-tag bg-cyan radius sm margin-left-sm"
                                style="vertical-align: middle">🌟 {{ item.release_type_name }}</view>
                            <view v-if="item.release_type == 3" class="cu-tag bg-blue radius sm margin-left-sm"
                                style="vertical-align: middle">⭐ {{ item.release_type_name }}</view>
                        </view>
                        <view style="margin: 15px 0px 0px 15px; font-size: 24rpx; color: #1cbbb4">
                            <text>📍 {{ item.work_address }}</text>
                        </view>
                        <view class="text_num_3" style="margin-left: 15px; margin-top: 15px; color: #666;">{{ item.job_description }}
                        </view>
                    </view>
                </view>

                <view v-if="current == 'tab2'" style="padding: 15px">
                    <button @tap.stop.prevent="DelInfo" :data-id="item.id" class="cu-btn round bg-gray" style="color: #000000;">🗑️ 删除</button>
                </view>
            </view>
            <view style="padding-bottom: 100px" :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>
        <view class="cu-bar tabbar bg-white" style="position: fixed; bottom: 0; width: 100%; z-index: 2000; box-shadow: 0 -4rpx 20rpx rgba(0,129,255,0.1);">
            <view @tap="handleChangeDi" data-key="tab1"
                :class="'action ' + (current == 'tab1' ? 'text-blue' : 'text-gray')" style="flex-direction: column; align-items: center;">
                <view style="font-size: 44rpx; margin-bottom: 10rpx;">🏠 </view>
                <text style="font-size: 22rpx;">首页</text>
            </view>
            <view class="action text-gray add-action">
                <button @tap="add_local" data-key="0" class="cu-btn cuIcon-add shadow text-white"
                    style="width: 100rpx; height: 100rpx; top: -50rpx; line-height: 100rpx; background: linear-gradient(45deg, #0081ff, #1cbbb4);"></button>
            </view>
            <view @tap="handleChangeDi" data-key="tab2"
                :class="'action ' + (current == 'tab2' ? 'text-blue' : 'text-gray')" style="flex-direction: column; align-items: center;">
                <view style="font-size: 44rpx; margin-bottom: 10rpx;">👤</view>
                <text style="font-size: 22rpx;">我的</text>
            </view>
        </view>
    </view>
</template>

<script>
import uiTab from '@/yl_welore/colorui/ui-tab/ui-tab';
const app = getApp();
var http = require('../../../util/http.js');

export default {
    components: {
        uiTab
    },
    data() {
        return {
            http_root: app.globalData.http_root,
            type_id: 0,
            current: 'tab1',
            page: 1,
            list: [],
            tab_list: [
                {
                    realm_name: '最新',
                    id: 0
                }
            ],
            my_list: [
                {
                    realm_name: '我发布的',
                    id: 0
                },
                {
                    realm_name: '我收藏的',
                    id: 1
                }
            ],
            content: '',
            design: {},
            is_collect: false,
            uid: '',
            di_msg: false
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.doIt();
    },
    onShow() {
        var lost = app.globalData.__PlugUnitScreen('7d6353619854f00edf2d1e9a44430333');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                this.BackPage();
            }, 1000);
            return;
        }
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        //模拟加载
        setTimeout(() => {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.list = [];
        this.page = 1;
        this.di_msg = false;
        this.get_list();
    },
    onReachBottom() {
        this.page = this.page + 1;
        this.get_list();
    },
    onShareAppMessage() {
        var info = this.design;
        return {
            title: info.custom_title_em,
            path: '/yl_welore/pages/packageE/employment_list/index'
        };
    },
    methods: {
        async doIt() {
            app.globalData.getLogin(
                // 成功回调 returnA
                async (userInfo) => {
                    console.log(' 登录成功:', userInfo);

                    this.get_list();
                    await this.get_lost_type();
                    var dd = uni.getStorageSync('is_diy');
                    if (dd) {
                        this.design = dd;
                    } else {
                        this.get_diy();
                    }
                },
                // 失败回调 returnB
                (err) => {
                    console.error(' 登录失败:', err);
                }
            );
        },
        checkToken() {
            return new Promise((resolve, reject) => {
                var e = app.globalData.getCache('userinfo');
                if (!e) {
                    resolve(e);
                    return 'no'; //执行登陆
                } else {
                    var b = app.globalData.api_root + 'Check/check_token';
                    var that = this;
                    var e = app.globalData.getCache('userinfo');
                    var params = new Object();
                    params.token = e.token;
                    params.openid = e.openid;
                    http.POST(b, {
                        params: params,
                        success: (res) => {
                            resolve(res);
                        },
                        fail: () => {
                            uni.showModal({
                                title: '提示',
                                content: '网络繁忙，请稍候重试！',
                                showCancel: false,
                                success: (res) => { }
                            });
                        }
                    });
                }
            });
        },
        getLogin() {
            return new Promise((resolve, reject) => {
                var that = this;
                uni.login({
                    success(res) {
                        var params = new Object();
                        params.code = res.code;
                        http.POST(app.globalData.api_root + 'Login/index', {
                            params: params,
                            success: (open) => {
                                console.log(open);
                                var data = new Object();
                                data.openid = open.data.info.openid;
                                data.session_key = open.data.info.session_key;
                                http.POST(app.globalData.api_root + 'Login/add_tourist', {
                                    params: data,
                                    success: (d) => {
                                        console.log(d);
                                        app.globalData.setCache('userinfo', d.data.info);
                                        resolve(d);
                                        that.uid = d.data.info.uid;
                                    }
                                });
                            }
                        });
                    }
                });
            });
        },
        DelInfo(d) {
            var that = this;
            var id = d.currentTarget.dataset.id;
            uni.showModal({
                title: '提示',
                content: '确定删除吗？',
                success(res) {
                    if (res.confirm) {
                        that.DelInfoDo(id);
                    }
                }
            });
        },
        DelInfoDo(id) {
            var b = app.globalData.api_root + 'Employment/DelInfoDo';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    });
                    that.page = 1;
                    that.list = [];
                    that.get_list();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        get_diy() {
            var b = app.globalData.api_root + 'User/get_diy';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    that.design = res.data;
                    uni.setStorageSync('is_diy', res.data);
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        get_ser_name(d) {
            this.content = d.detail.value;
        },
        sou() {
            this.list = [];
            this.page = 1;
            this.type_id = 0;
            this.di_msg = false;
            this.get_list();
        },
        handleChangeDi(detail) {
            var key = detail.currentTarget.dataset.key;
            console.log(key);
            this.list = [];
            this.page = 1;
            this.current = key;
            this.type_id = 0;
            this.di_msg = false;
            this.is_collect = key=='tab1'?0:1;
            this.get_list();
        },
        handleChange(detail) {
            var id = detail.detail.data.id;
            this.list = [];
            this.page = 1;
            this.type_id = id;
            this.di_msg = false;
            this.is_collect = 0;
            this.get_list();
        },
        handleChangeKey(d) {
            console.log(d);
            this.list = [];
            this.page = 1;
            this.type_id = 0;
            this.di_msg = false;
            this.is_collect = 1;
            this.get_list();
        },
        get_lost_type() {
            var b = app.globalData.api_root + 'Employment/getLostType';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    var tab_list = that.tab_list;
                    tab_list.push(...res.data);
                    that.tab_list = tab_list;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        add_local() {
            var e = app.globalData.getCache('userinfo');
            if (e.tourist == 1) {
                uni.showModal({
                    title: '提示',
                    content: '请登录后发布内容！',
                    showCancel: false,
                    confirmText: '去登陆',
                    success: (res) => {
                        uni.reLaunch({
                            url: '/yl_welore/pages/user/index'
                        });
                    }
                });
            }
            uni.navigateTo({
                url: '/yl_welore/pages/packageE/employment_add/index'
            });
        },
        get_url(d) {
            console.log(d);
            var id = d.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageE/employment_info/index?id=' + id
            });
        },
        get_list() {
            var b = app.globalData.api_root + 'Employment/getLostList';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            params.type_id = this.type_id;
            params.current = this.current;
            params.search = this.content;
            params.is_collect = this.is_collect ? 1 : 0;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.length == 0 || res.data.length < 7) {
                        that.di_msg = true;
                    }
                    var list = that.list;
                    list.push(...res.data);
                    that.list = list;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background: linear-gradient(180deg, #e6f3ff 0%, #f0f9ff 50%, #ffffff 100%);
    background-attachment: fixed;
    background-size: 100% 100%;
}

._this {
    font-weight: 600;
    font-size: 20px;
}

/* 自定义卡片样式 */
.text_num_3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 24rpx;
    color: #888;
    line-height: 1.5;
}

/* 自定义标签样式 */
.cu-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0 16rpx;
    font-size: 22rpx;
    height: 40rpx;
    color: #ffffff;
}

/* 自定义动画效果 */
.animation-slide-right {
    animation: fadeIn 0.5s ease-out;
    min-height: 100vh;
}

@keyframes fadeIn {
    from {
        opacity: 0.5;
    }
    to {
        opacity: 1;
    }
}

.mask1 {
    height: 48px;
    width: 48px;
    background-color: #ffffff;
    opacity: 0.85;
    z-index: 1000;
    border-radius: 750rpx;
    transform: scale(0);
    position: fixed;
    bottom: 0;
    left: 50%;
    margin-left: -24px;
}

.maskOpen {
    animation: maskO 0.5s both;
}

.maskClose {
    animation: maskC 0.3s both;
}

@keyframes maskO {
    0% {
        transform: scale(0);
    }

    20% {
        transform: scale(4);
    }

    40% {
        transform: scale(18);
    }

    60% {
        transform: scale(24);
    }

    80% {
        transform: scale(38);
    }

    100% {
        transform: scale(48);
    }
}

@keyframes maskC {
    0% {
        transform: scale(48);
    }

    25% {
        transform: scale(24);
    }

    100% {
        transform: scale(0);
    }
}

.btn {
    width: 100%;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    z-index: 1005;
    bottom: calc(10rpx + env(safe-area-inset-bottom));
}

.btn-main {
    border-radius: 50%;
    z-index: 1005;
    height: 48px;
    font-size: 28px;
    width: 48px;
    line-height: 48px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 129, 255, 0.3);
    color: #fff;
    background: linear-gradient(45deg, #0081ff, #1cbbb4);
}

.menuOpen {
    animation: menuO 0.3s both;
}

.menuClose {
    animation: menuC 0.3s both;
}

@keyframes menuO {
    0% {
        transform: rotate(0deg);
        color: #fff;
        background: linear-gradient(45deg, #0081ff, #1cbbb4);
    }

    100% {
        transform: rotate(45deg);
        color: #000;
        background: #fff;
    }
}

@keyframes menuC {
    0% {
        transform: rotate(45deg);
        color: #000;
        background: #fff;
    }

    100% {
        transform: rotate(-0deg);
        color: #fff;
        background: linear-gradient(45deg, #0081ff, #1cbbb4);
    }
}

.menu-container {
    position: fixed;
    width: 100%;
    z-index: 1002;
    bottom: 0rpx;
}

.add_menu {
    padding-bottom: calc(48px + 40rpx + env(safe-area-inset-bottom));
}

.menu-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    padding-bottom: 15rpx;
}

.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: bounceInDown 0.45s linear both;
}

.menu-icon {
    width: 110rpx;
    height: 110rpx;
    margin-bottom: 15rpx;
}

.menu-name {
    color: #333;
    font-size: 25rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
    letter-spacing: 1px;
}

@keyframes bounceInDown {
    0% {
        opacity: 0;
        transform: translateY(100%);
    }

    60% {
        transform: translateY(-10%);
    }

    80% {
        transform: translateY(10%);
    }

    100% {
        opacity: 1;
        transform: translateY(0%);
    }
}
</style>
