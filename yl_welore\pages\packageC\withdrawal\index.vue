<template>
    <view>
        <view class="nav-wrap" style="background-image: linear-gradient(to top, #4481eb 0%, #04befe 100%)">
            <cu-custom :isSearch="false" :isBack="true">
                <view slot="backText">返回</view>
                <view slot="content" style="color: #fff; font-weight: 600; font-size: 36rpx">提现</view>
            </cu-custom>
            <view style="width: 100%; min-height: 90px; margin: 0px auto; color: #4481eb; font-size: 14px">
                <view class="padding">
                    <view class="bg-white grid col-3 margin-bottom text-center" style="border-radius: 5px">
                        <view class="placeholder">
                            <image src="/static/yl_welore/style/icon/qianbao.png" style="width: 30px; height: 30px; vertical-align: middle"></image>
                            <text style="margin-left: 5px; vertical-align: middle">我的钱包</text>
                        </view>
                        <view class="placeholder">
                            <image src="/static/yl_welore/style/icon/zhuanzhang.png" style="width: 20px; height: 16px; vertical-align: middle"></image>
                        </view>
                        <view v-if="setting.open_offline_payment == 0" class="placeholder">
                            <image src="/static/yl_welore/style/icon/lingqian.png" style="width: 20px; height: 20px; vertical-align: middle"></image>
                            <text style="margin-left: 5px; vertical-align: middle">微信零钱</text>
                        </view>
                        <view v-if="setting.open_offline_payment == 1" class="placeholder">
                            <image src="/static/yl_welore/style/icon/yinhangka.png" style="width: 25px; height: 25px; vertical-align: middle"></image>
                            <text style="margin-left: 5px; vertical-align: middle">支付宝</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view style="background-color: #ffffff; padding-top: 10px">
            <view style="font-size: 16px; margin-left: 15px; color: #4481eb">
                <text style="font-size: 14px; color: #999999">
                    可用余额：{{ setting.user_info.conch }}，最低提现额度：{{ setting.lowest_money }}，提现将会扣除提现金额的{{ setting.payment_tariff * 100 }}%作为手续费。
                </text>
            </view>
            <view style="color: #4481eb">
                <text style="float: left; font-size: 40px; margin-left: 10px">￥</text>
                <input
                    type="number"
                    :focus="true"
                    :value="withdraw_money"
                    @input="get_money"
                    placeholder="整数金额"
                    maxlength="10"
                    style="padding-left: 10px; border-bottom: 1px #f1f1f1 solid; float: left; width: 82%; font-size: 20px; height: 43px; margin-top: 7px"
                />
            </view>
            <view style="clear: both; height: 0"></view>
            <view style="color: #4481eb" v-if="setting.open_offline_payment == 1">
                <input
                    type="text"
                    :value="withdraw_number"
                    :focus="true"
                    @input="get_withdraw_card"
                    placeholder="支付宝帐号"
                    style="padding-left: 10px; border-bottom: 1px #f1f1f1 solid; width: 82%; font-size: 20px; height: 43px; margin-top: 7px"
                />
            </view>
            <view style="clear: both; height: 0"></view>
            <navigator url="/yl_welore/pages/packageC/notice/index" hover-class="none">
                <view style="width: 95%; text-align: center; padding: 10px; color: #5197e4; font-size: 14px">提现说明</view>
            </navigator>
        </view>
        <navigator url="/yl_welore/pages/packageC/withdrawal_list/index" hover-class="none">
            <view style="margin-top: 20px; color: #000000; font-size: 12px; text-align: center">
                <i-icon type="prompt" />
                提现明细
            </view>
        </navigator>
        <button @tap="withdrawFun" style="width: 80%; height: 40px; line-height: 40px; border-radius: 50px; margin-top: 30px; background-color: #ff9933; color: #fff">确定</button>

        <view :class="'cu-modal ' + (withdraw ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">提现确认</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding-xl">确定要提现吗？</view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="withdraw_do">确定</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<!-- <script module="filters" lang="wxs" src="@/yl_welore/pages/packageC/withdrawal/tofix.wxs"></script> -->
<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            setting: {
                open_offline_payment: 0,
                user_info: {
                    conch: ''
                },
                lowest_money: '',
                payment_tariff: 0
            },
            withdraw: false,
            withdraw_money: '',
            withdraw_number: '',
            page: 0
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.page = 1;
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.get_raws_setting();
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },

    methods: {
        /**
         * 获取提现配置
         */
        get_raws_setting() {
            var b = app.globalData.api_root + 'User/get_raws_setting';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success: function (res) {
                    that.setting = res.data;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 记录卡号
         */
        get_withdraw_card(e) {
            this.withdraw_number = e.detail.value;
        },

        /**
         * 记录提醒金额
         */
        get_money(e) {
            var str2 = e.detail.value.replace('.', '');
            this.withdraw_money = str2;
        },

        /**
         * 提现确认
         */
        withdrawFun() {
            if (this.setting.open_offline_payment == 1) {
                if (this.withdraw_number == '') {
                    uni.showToast({
                        title: '支付宝账号不能为空',
                        icon: 'none',
                        duration: 2000
                    });
                    return false;
                }
            }
            if (this.withdraw_money == '' || this.withdraw_money <= 0) {
                uni.showToast({
                    title: '提现金额不正确',
                    icon: 'none',
                    duration: 2000
                });
                return false;
            }
            this.withdraw = true;
        },

        hideModal() {
            this.withdraw = false;
        },

        /**
         * 确认提现
         */
        withdraw_do() {
            this.hideModal();
            uni.showLoading({
                title: '提现中...'
            });
            if (this.setting.open_offline_payment == 1) {
                if (this.withdraw_number == '') {
                    uni.showToast({
                        title: '支付宝账号不能为空',
                        icon: 'none',
                        duration: 2000
                    });
                    return false;
                }
            }
            if (this.withdraw_money == '' || this.withdraw_money <= 0) {
                uni.showToast({
                    title: '提现金额不正确',
                    icon: 'none',
                    duration: 2000
                });
                return false;
            }
            var b = app.globalData.api_root + 'User/withdraw';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.withdraw_number = this.withdraw_number;
            params.withdraw_money = this.withdraw_money;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    that.hideModal();
                    uni.hideLoading();
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                    that.withdraw_money = '';
                    that.withdraw_number = '';
                    that.get_raws_setting();
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        navbackFun() {
            uni.navigateBack();
        }
    }
};
</script>
<style>
page {
    background-color: #f7f7f7;
}
.placeholder {
    text-align: center;
    line-height: 4.3em;
}
button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
}

button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
    overflow: inherit;
}
</style>
