<template>
    <view>
        <cu-custom bgColor="notes_top_color" :isSearch="false" :isBack="true" style="color: #ffffff" TextColor="rgb(24,18,40)">
            <view slot="backText"></view>
            <view slot="content" style="color: #ffffff; font-weight: 600; font-size: 36rpx">{{ info.custom_title }}</view>
        </cu-custom>
        <view
            :style="
                'font-weight: 300;background-image:url(' +
                http_root +
                'addons/yl_welore/web/static/mineIcon/notes/zt_bj.jpg);background-repeat: no-repeat;background-size: 100% 100%;margin: 0rpx; padding: 0rpx; display:block;'
            "
        >
            <view class="padding flex text-center text-grey" style="border-radius: 10px">
                <view class="flex flex-sub flex-direction solid-right">
                    <view class="text-xxl text-green">{{ visitTotal }}张</view>
                    <view class="margin-top-sm text-white">
                        <text class="cuIcon-group_fill lg"></text>
                        全部
                    </view>
                </view>
                <view class="flex flex-sub flex-direction solid-right">
                    <view class="text-xxl text-cyan">{{ starCount }}张</view>
                    <view class="margin-top-sm text-white">
                        <text class="cuIcon-male lg"></text>
                        男生
                    </view>
                </view>
                <view class="flex flex-sub flex-direction">
                    <view class="text-xxl text-pink">{{ forksCount }}张</view>
                    <view class="margin-top-sm text-white">
                        <text class="cuIcon-female lg"></text>
                        女生
                    </view>
                </view>
                <view class="flex flex-sub flex-direction" style="margin-top: -5px">
                    <view @tap="opne_my_notes" data-key="2" class="text-lg bg-orange radius" style="padding: 5px 0px">抽到的</view>
                    <view @tap="opne_my_notes" data-key="1" class="text-lg margin-top-sm bg-mauve radius" style="padding: 5px 0px">我放的</view>
                </view>
            </view>
            <view class="cu-bar margin-top">
                <view class="action text-white">
                    <text class="cuIcon-title text-yellow"></text>
                    放入
                </view>
            </view>
            <view class="padding">
                <view class="flex">
                    <view class="flex-sub padding-xs" @tap="f_notes" data-key="1">
                        <image class="now_level" mode="widthFix" style="width: 100%" :src="http_root + 'addons/yl_welore/web/static/mineIcon/notes/f_nan.png'"></image>
                    </view>
                    <view class="flex-sub padding-xs" @tap="f_notes" data-key="0">
                        <image class="now_level" mode="widthFix" style="width: 100%" :src="http_root + 'addons/yl_welore/web/static/mineIcon/notes/f_nv.png'"></image>
                    </view>
                </view>
            </view>
            <view class="cu-bar">
                <view class="action text-white">
                    <text class="cuIcon-title text-purple"></text>
                    随机
                </view>
            </view>
            <view class="padding">
                <view class="flex">
                    <view @tap="smoke_random" data-key="1" class="flex-sub padding-xs">
                        <image class="now_level" mode="widthFix" style="width: 100%" :src="http_root + 'addons/yl_welore/web/static/mineIcon/notes/nan.png'"></image>
                    </view>
                    <view @tap="smoke_random" data-key="0" class="flex-sub padding-xs">
                        <image class="now_level" mode="widthFix" style="width: 100%" :src="http_root + 'addons/yl_welore/web/static/mineIcon/notes/nv.png'"></image>
                    </view>
                </view>
            </view>
            <view class="cu-bar">
                <view class="action text-white">
                    <text class="cuIcon-title text-pink"></text>
                    同城
                </view>
            </view>
            <view class="padding">
                <view class="flex">
                    <view class="flex-sub padding-xs">
                        <image
                            @tap="smoke_area"
                            data-key="1"
                            class="now_level"
                            mode="widthFix"
                            style="width: 100%"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/notes/t_nan.png'"
                        ></image>
                    </view>
                    <view class="flex-sub padding-xs">
                        <image
                            @tap="smoke_area"
                            data-key="0"
                            class="now_level"
                            mode="widthFix"
                            style="width: 100%"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/notes/t_nv.png'"
                        ></image>
                    </view>
                </view>
            </view>
            <view class="cu-bar">
                <view class="action text-white">
                    <text class="cuIcon-title text-cyan"></text>
                    星座
                </view>
            </view>
            <view class="padding" style="padding-bottom: 40px">
                <view class="flex">
                    <view class="flex-sub padding-xs">
                        <image
                            @tap="smoke_xing"
                            data-key="1"
                            class="now_level"
                            mode="widthFix"
                            style="width: 100%"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/notes/x_nan.png'"
                        ></image>
                    </view>
                    <view class="flex-sub padding-xs">
                        <image
                            @tap="smoke_xing"
                            data-key="0"
                            class="now_level"
                            mode="widthFix"
                            style="width: 100%"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/notes/x_nv.png'"
                        ></image>
                    </view>
                </view>
            </view>
        </view>

        <view v-if="modal == 'f_modal'" :class="'cu-modal bottom-modal ' + (modal == 'f_modal' ? 'show' : '')">
            <view class="cu-dialog" style="height: 80%; border-radius: 10px">
                <view class="cu-bar bg-white">
                    <view class="content font_weight">
                        <text>放入一张</text>
                        <text v-if="f_key == 1" style="color: #0099ff; font-weight: 500; font-size: 18px">男生</text>
                        <text v-if="f_key == 0" style="color: #ff6699; font-weight: 500; font-size: 18px">女生</text>
                        <text>纸条</text>
                    </view>
                    <view class="action text-green"></view>
                    <view class="action font_weight" @tap="hideModal">取消</view>
                </view>
                <scroll-view scroll-y style="height: 93%" class="padding">
                    <view class="cu-form-group">
                        <view class="title">联系方式</view>
                        <input @input="contact_person_input" style="text-align: right" placeholder="微信号 / QQ" />
                    </view>
                    <view class="cu-form-group margin-top">
                        <view class="title">星座</view>
                        <picker @change="PickerChange" :value="con_index" :range="constellation">
                            <view class="picker">
                                {{ con_index ? constellation[con_index] : '我的星座' }}
                            </view>
                        </picker>
                    </view>
                    <view class="bg-white margin-top padding-bottom">
                        <view class="flex p-xs">
                            <view class="flex-twice" style="padding: 30rpx 0px">
                                <picker @change="bindPickerChange" :value="area_index" :range="area_array" range-key="name">
                                    <text class="cuIcon-location lg text-gray" style="font-size: 15px"></text>
                                    <text style="margin: 0px 5px">位置：</text>
                                    <text style="margin: 0px 5px">{{ area_name }}</text>
                                    <text class="cuIcon-right lg text-gray"></text>
                                </picker>
                            </view>
                            <view class="flex-sub" style="padding: 30rpx 0px">
                                <picker @change="bindPickerOld" :value="old_index" :range="old_array">
                                    <view class="picker">
                                        <text class="cuIcon-evaluate_fill lg text-gray" style="font-size: 15px"></text>
                                        <text style="margin: 0px 5px">年龄：</text>
                                        <text>{{ old_array[old_index] }}</text>
                                        <text class="cuIcon-right lg text-gray"></text>
                                    </view>
                                </picker>
                            </view>
                        </view>
                        <view class="cu-form-group">
                            <textarea style="text-align: left" maxlength="1000" @input="textareaAInput" placeholder="介绍自己或对另一半的期待..."></textarea>
                        </view>
                        <view class="cu-form-group" style="border-top: 0px">
                            <view class="grid col-4 grid-square flex-sub">
                                <view class="bg-img" @tap="ViewImage" :data-url="imgList[index]" v-for="(item, index) in imgList" :key="index">
                                    <image :src="imgList[index]" mode="aspectFill" style="left: 0"></image>

                                    <view class="cu-tag bg-red" @tap.stop.prevent="DelImg" :data-index="index" style="z-index: 1">
                                        <text class="cuIcon-close"></text>
                                    </view>
                                </view>
                                <view class="solids" @tap="chooseImage" v-if="imgList.length < 3">
                                    <text class="cuIcon-cameraadd"></text>
                                </view>
                            </view>
                        </view>
                        <view class="grid col-1 text-center margin-top">
                            <view class="padding-sm">
                                <picker @change="bindPickerChangeLife" :value="life_index" :range="life_array">
                                    <text class="cuIcon-time lg text-gray" style="font-size: 15px"></text>
                                    <text style="margin: 0px 5px">纸条的生命：</text>
                                    <text v-if="life_index != 0">被抽{{ life_array[life_index] }}次后失效</text>
                                    <text v-if="life_index == 0">不限制</text>
                                    <text class="cuIcon-right lg text-gray"></text>
                                </picker>
                            </view>
                        </view>
                        <view class="grid col-1 text-center">
                            <view class="padding-sm">
                                <picker @change="bindPickerChangeChou" :value="c_area_index" :range="c_area_array" range-key="name">
                                    <text class="cuIcon-wefill lg text-gray" style="font-size: 15px"></text>
                                    <text>限定抽的用户位置：</text>
                                    <text style="margin: 0px 5px">{{ c_area_name }}</text>
                                    <text class="cuIcon-right lg text-gray"></text>
                                </picker>
                            </view>
                        </view>
                        <view class="margin-top font_weight text-yellow">
                            <block v-if="f_key == 1">
                                <text>{{ info.throw_price_male }}</text>
                            </block>
                            <block v-if="f_key == 0">
                                <text>{{ info.throw_price_female }}</text>
                            </block>
                            <text v-if="info.pay_type == 0">{{ info.confer }}</text>
                            <text v-if="info.pay_type == 1">{{ info.currency }}</text>
                            <text v-if="info.pay_type == 2">元</text>
                            <text>/次</text>
                        </view>
                        <view @tap="pay_type_do" class="bg-yellow padding radius text-center shadow-blur margin-top">
                            <view>确定投入</view>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>

        <view :class="'cu-modal bottom-modal ' + (modal == 'smoke' ? 'show' : '')">
            <view class="cu-dialog" style="height: 50%; border-radius: 10px">
                <view class="cu-bar bg-white">
                    <view class="content font_weight">
                        <text>随机抽</text>
                        <text v-if="c_key == 1" style="color: #0099ff; font-weight: 500; font-size: 18px">男生</text>
                        <text v-if="c_key == 0" style="color: #ff6699; font-weight: 500; font-size: 18px">女生</text>
                        <text>纸条</text>
                    </view>
                    <view class="action text-green"></view>
                    <view class="action text-blue" @tap="hideModal">取消</view>
                </view>
                <view class="padding">
                    <view class="cu-form-group margin-top">
                        <view class="title">所在地区</view>
                        <picker @change="bindPickerChangeChou" :value="c_area_index" :range="c_area_array" range-key="name">
                            <view class="picker">
                                {{ c_area_name }}
                            </view>
                        </picker>
                    </view>
                    <view class="cu-form-group margin-top">
                        <view class="title">年龄</view>
                        <picker @change="bindPickerOld" :value="c_area_index" :range="old_array">
                            <view class="picker">
                                {{ old_array[old_index] }}
                            </view>
                        </picker>
                    </view>
                    <view class="margin-top font_weight text-yellow">
                        <block v-if="c_key == 1">
                            <text>{{ info.pick_price_male }}</text>
                        </block>
                        <block v-if="c_key == 0">
                            <text>{{ info.pick_price_female }}</text>
                        </block>
                        <text v-if="info.pay_type == 0">{{ info.confer }}</text>
                        <text v-if="info.pay_type == 1">{{ info.currency }}</text>
                        <text v-if="info.pay_type == 2">元</text>
                        <text>/次</text>
                    </view>
                </view>
                <view @tap="pay_smoke_do" class="bg-yellow padding radius text-center shadow-blur margin-top">
                    <view>确定抽一张</view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal bottom-modal ' + (modal == 'area_smoke' ? 'show' : '')">
            <view class="cu-dialog" style="height: 43%; border-radius: 10px">
                <view class="cu-bar bg-white">
                    <view class="content font_weight">
                        <text>抽同城</text>
                        <text v-if="c_key == 1" style="color: #0099ff; font-weight: 500; font-size: 18px">男生</text>
                        <text v-if="c_key == 0" style="color: #ff6699; font-weight: 500; font-size: 18px">女生</text>
                        <text>纸条</text>
                    </view>
                    <view class="action text-green"></view>
                    <view class="action text-blue" @tap="hideModal">取消</view>
                </view>
                <view class="padding">
                    <view class="cu-form-group margin-top">
                        <view class="title">我的地区</view>
                        <picker @change="bindPickerChangeChouArea" :value="a_area_index" :range="a_area_array" range-key="name">
                            <view class="picker">
                                {{ a_area_name }}
                            </view>
                        </picker>
                    </view>
                    <view class="cu-form-group margin-top">
                        <view class="title">年龄</view>
                        <picker @change="bindPickerOld" :value="c_area_index" :range="old_array">
                            <view class="picker">
                                {{ old_array[old_index] }}
                            </view>
                        </picker>
                    </view>
                    <view class="margin-top font_weight text-yellow">
                        <block v-if="c_key == 1">
                            <text>{{ info.pick_price_male }}</text>
                        </block>
                        <block v-if="c_key == 0">
                            <text>{{ info.pick_price_female }}</text>
                        </block>
                        <text v-if="info.pay_type == 0">{{ info.confer }}</text>
                        <text v-if="info.pay_type == 1">{{ info.currency }}</text>
                        <text v-if="info.pay_type == 2">元</text>
                        <text>/次</text>
                    </view>
                </view>
                <view @tap="pay_smoke_do" class="bg-yellow padding radius text-center shadow-blur margin-top">
                    <view>确定抽一张</view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal bottom-modal ' + (modal == 'xing_smoke' ? 'show' : '')">
            <view class="cu-dialog" style="height: 43%; border-radius: 10px">
                <view class="cu-bar bg-white">
                    <view class="content font_weight">
                        <text>星座</text>
                        <text v-if="c_key == 1" style="color: #0099ff; font-weight: 500; font-size: 18px">男生</text>
                        <text v-if="c_key == 0" style="color: #ff6699; font-weight: 500; font-size: 18px">女生</text>
                        <text>纸条</text>
                    </view>
                    <view class="action text-green"></view>
                    <view class="action text-blue" @tap="hideModal">取消</view>
                </view>
                <view class="padding">
                    <view class="cu-form-group margin-top">
                        <view class="title">我的星座</view>
                        <picker @change="PickerChange" :value="con_index" :range="constellation">
                            <view class="picker">
                                {{ con_index ? constellation[con_index] : '不限' }}
                            </view>
                        </picker>
                    </view>
                    <view class="margin-top font_weight text-yellow">
                        <block v-if="c_key == 1">
                            <text>{{ info.pick_price_male }}</text>
                        </block>
                        <block v-if="c_key == 0">
                            <text>{{ info.pick_price_female }}</text>
                        </block>
                        <text v-if="info.pay_type == 0">{{ info.confer }}</text>
                        <text v-if="info.pay_type == 1">{{ info.currency }}</text>
                        <text v-if="info.pay_type == 2">元</text>
                        <text>/次</text>
                    </view>
                </view>
                <view @tap="pay_smoke_do" class="bg-yellow padding radius text-center shadow-blur margin-top">
                    <view>确定抽一张</view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal ' + (smoke_mod == 'show' ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">
                        <text>抽到一张</text>
                        <text v-if="c_key == 1" style="color: #0099ff; font-weight: 500; font-size: 18px">男生</text>
                        <text v-if="c_key == 0" style="color: #ff6699; font-weight: 500; font-size: 18px">女生</text>
                        <text>纸条</text>
                    </view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding bg-white">
                    <view style="font-size: 18px" class="padding-bottom font_weight text-left">{{ smoke_info['hedge_content'] }}</view>
                    <view class="text-left" v-if="smoke_info['image_part'].length > 0">
                        <block v-for="(item, pp_index) in smoke_info['image_part']" :key="pp_index">
                            <image @tap="previewImage" :data-key="pp_index" :src="item" mode="aspectFit" style="height: 80px; width: 80px"></image>
                        </block>
                    </view>
                    <view class="solid-bottom"></view>
                    <view class="padding font_weight text-left">
                        <text style="vertical-align: middle" v-if="c_key == 1">他</text>
                        <text style="vertical-align: middle" v-if="c_key == 0">她</text>
                        <text style="vertical-align: middle">留的联系方式：</text>
                        <text :user-select="true" style="vertical-align: middle">{{ smoke_info['contact_person'] }}</text>
                        <button @tap="copyBtn" :data-key="smoke_info.contact_person" class="cu-btn round bg-green button-hover sm margin-left">点击复制</button>
                    </view>
                    <view class="text-yellow bg-yellow radius light">
                        <view class="padding text-left">
                            <view class="font_weight">
                                <text class="cuIcon-info lg"></text>
                                纸条内容为网友发布，平台不保证真实性，请注意甄别！如对方要求打赏，转账等行为请勿轻信，谨防上当受骗！
                            </view>
                            <view class="font_weight margin-top-xs">
                                <text class="cuIcon-info lg"></text>
                                抽到的纸条可前往
                                <text class="text-blue">我的纸条</text>
                                页面查看
                            </view>
                        </view>
                    </view>
                    <view @tap="hideModal" class="bg-mauve padding round text-center shadow-blur margin-top">
                        <view>确认</view>
                    </view>
                </view>
            </view>
        </view>

        <view :class="'cu-modal bottom-modal ' + (modal == 'notice' ? 'show' : '')" @touchmove.stop.prevent="doNothing" style="text-align: inherit">
            <view class="cu-dialog">
                <view class="cu-bar bg-white">
                    <view class="content">用户须知</view>
                    <view class="action text-green"></view>
                    <view class="action" @tap="hideModal_notice">确定</view>
                </view>
                <scroll-view :scroll-y="true" style="height: 800rpx" class="padding-xl">
                    <rich-text :nodes="info.notice"></rich-text>
                </scroll-view>
            </view>
        </view>

        <login id="login" :check_user_login="check_user_login"></login>
    </view>
</template>

<script>
import login from '@/yl_welore/util/user_login/login';
const app = getApp();
const STORAGE_KEY = 'PLUG-ADD-NOTICE-KEY';
var http = require('../../../util/http.js');
var area = require('../../../util/area.js');
var md5 = require('../../../util/md5.js');
import regeneratorRuntime from '../../../util/runtime';
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
        check_user_login: false,
        http_root: app.globalData.http_root,
        info: '',
        starCount: 0,
        forksCount: 0,
        visitTotal: 0,
        f_key: 1,
        //1男0女
        c_key: 1,
        modal: null,
        constellation: ['白羊座', '金牛座', '双子座', '巨蟹座', '狮子座', '处女座', '天秤座', '天蝎座', '射手座', '魔羯座', '水瓶座', '双鱼座'],
        con_index: null,
        old_array: ['不限', 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50],
        old_index: 0,
        old_name: 0,
        area_array: area['areaList'],
        area_index: null,
        area_name: '',
        c_area_array: area['areaList'],
        c_area_index: 0,
        c_area_name: '不限',
        a_area_array: area['areaList'],
        a_area_index: 0,
        a_area_name: '不限',
        c_my_area_id: 0,
        life_array: ['不限', 30, 50, 100, 200, 300],
        life_index: 0,
        life_name: 0,
        imgList: [],
        hedge_content: '',
        contact_person: '',
        smoke_mod: null,
        smoke_info: '',
        c_type: 1 //1随机2同城
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var that = this;
        uni.showShareMenu({
            menus: ['shareAppMessage', 'shareTimeline']
        });
        this.LogindoIt();
        //area['areaList']
        if (this.area_array[0].id != 0) {
            var area_array = this.area_array;
            area_array.unshift({
                id: 0,
                name: '隐藏'
            });
            var c_area_array = this.c_area_array;
            c_area_array.unshift({
                id: 0,
                name: '不限制'
            });
            var a_area_array = this.a_area_array;
            a_area_array.unshift({
                id: 0,
                name: '不限制'
            });
            this.area_array = area_array;
            this.c_area_array = c_area_array;
            this.a_area_array = a_area_array;
        }
        this.modal = null;
        this.con_index = null;
        this.old_index = 0;
        this.old_name = 0;
        this.area_index = null;
        this.area_name = '';
        this.life_index = 0;
        this.life_name = 0;
        this.imgList = [];
        this.hedge_content = '';
        this.contact_person = '';
        //this.hideModal();
        //查询缓存
        var over_time = app.globalData.getCache(STORAGE_KEY);
        var this_time = parseInt(+new Date() / 1000);
        if (over_time <= this_time) {
            this.modal = 'notice';
        }

        //wx.hideShareMenu();
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage(d) {
        return {
            title: '小纸条',
            path: '/yl_welore/pages/packageE/notes/index'
        };
    },

    onShareTimeline() {
        return {
            title: '小纸条',
            path: '/yl_welore/pages/packageE/notes/index'
        };
    },

    methods: {
        async LogindoIt() {
        var e = app.globalData.getCache('userinfo');
        const do0 = await this.checkToken();
        if (!e || do0.data.status == 'no') {
            //缓存为空执行登陆
            const do1 = await this.getLogin();
        } else {
            //字段为空执行登陆
            if (typeof e.token_impede == 'undefined') {
                const do1 = await this.getLogin();
            } else {
                //字段0或者小于当前时间执行登陆
                var t = parseInt(+new Date() / 1000);
                if (e.token_impede == 0 || t >= e.token_impede) {
                    const do1 = await this.getLogin();
                }
            }
        }
        const do2 = await this.tape();
    },
    getLogin() {
        return new Promise((resolve, reject) => {
            var that = this;
            uni.login({
                success(res) {
                    var params = new Object();
                    params.code = res.code;
                    http.POST(app.globalData.api_root + 'Login/index', {
                        params: params,
                        success: (open) => {
                            console.log(open);
                            var data = new Object();
                            data.openid = open.data.info.openid;
                            data.session_key = open.data.info.session_key;
                            http.POST(app.globalData.api_root + 'Login/add_tourist', {
                                params: data,
                                success: (d) => {
                                    console.log(d);
                                    app.globalData.setCache('userinfo', d.data.info);
                                    resolve(d);
                                }
                            });
                        }
                    });
                }
            });
        });
    },
    checkToken() {
        return new Promise((resolve, reject) => {
            var e = app.globalData.getCache('userinfo');
            if (!e) {
                resolve(e);
                return 'no'; //执行登陆
            } else {
                var b = app.globalData.api_root + 'Check/check_token';
                var that = this;
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        resolve(res);
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                });
            }
        });
    },

    hideModal_notice() {
        this.modal = null;
        var n = parseInt(+new Date() / 1000) + 86400;
        app.globalData.setCache(STORAGE_KEY, n);
    },
    opne_my_notes(e) {
        var a = app.globalData.getCache('userinfo');
        if (a.tourist == 1) {
            this.check_user_login = true;
            return;
        }
        var key = e.currentTarget.dataset.key;
        uni.navigateTo({
            url: '/yl_welore/pages/packageE/notes_my/index?key=' + key
        });
    },
    smoke_xing(e) {
        var a = app.globalData.getCache('userinfo');
        if (a.tourist == 1) {
            this.check_user_login = true;
            return;
        }
        var index = e.currentTarget.dataset.key;
        this.modal = 'xing_smoke';
        this.c_key = index;
        this.c_type = 3;
    },
    smoke_area(e) {
        var a = app.globalData.getCache('userinfo');
        if (a.tourist == 1) {
            this.check_user_login = true;
            return;
        }
        var index = e.currentTarget.dataset.key;
        this.modal = 'area_smoke';
        this.c_key = index;
        this.c_type = 2;
    },
    smoke_random(e) {
        var a = app.globalData.getCache('userinfo');
        if (a.tourist == 1) {
            this.check_user_login = true;
            return;
        }
        var index = e.currentTarget.dataset.key;
        this.modal = 'smoke';
        this.c_key = index;
        this.c_type = 1;
    },
    tape() {
        return new Promise((resolve, reject) => {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            var b = app.globalData.api_root + 'Tape/tape_info';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res.data);
                    let index = that.area_array.findIndex((item) => {
                        return item.name == res.data.city.province;
                    });
                    let c_index = that.c_area_array.findIndex((item) => {
                        return item.name == res.data.city.province;
                    });
                    if (typeof that.c_area_array[c_index] == 'undefined') {
                        uni.showModal({
                            title: '提示',
                            content: '腾讯地图key,设置错误！',
                            showCancel: false,
                            success: (res) => {}
                        });
                    } else {
                        that.info = res.data;
                        that.area_index = that.area_array[index]['id'];
                        that.c_my_area_id = that.c_area_array[c_index]['id'];
                        that.area_name = that.area_array[index]['name'];
                        that.a_area_index = that.area_array[index]['id'];
                        that.a_area_name = that.area_array[index]['name'];
                    }
                    that.doIt(res.data);
                    resolve(res);
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        });
    },
    pay_smoke_do() {
        var that = this;
        uni.showLoading({
            title: '为您抽取中...',
            mask: true
        });
        setTimeout(() => {
            if (that.c_key == 1) {
                //男
                if (that.info['pick_price_male'] > 0) {
                    if (that.info['pay_type'] == 2) {
                        that.pay_smoke_submit();
                    } else {
                        that.do_smoke_it();
                    }
                } else {
                    that.do_smoke_it();
                }
            } else {
                if (that.info['pick_price_female'] > 0) {
                    if (that.info['pay_type'] == 2) {
                        that.pay_smoke_submit();
                    } else {
                        that.do_smoke_it();
                    }
                } else {
                    that.do_smoke_it();
                }
            }
        }, 1500);
    },
    do_smoke_it(number = 0) {
        var that = this;
        var e = app.globalData.getCache('userinfo');
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        params.c_area_index = this.c_area_index;
        params.a_area_index = this.a_area_index;
        params.old_name = this.old_name;
        params.gender = this.c_key;
        params.number = number;
        params.c_my_area_id = this.c_my_area_id;
        params.c_type = this.c_type;
        params.constellation = this.con_index;
        var b = app.globalData.api_root + 'Tape/do_smoke_tape';
        http.POST(b, {
            params: params,
            success: (res) => {
                console.log(res);
                uni.hideLoading();
                if (res.data.code == 1) {
                    uni.showModal({
                        title: '提示',
                        content: res.data.msg,
                        showCancel: false,
                        success: (res) => {}
                    });
                } else {
                    that.modal = null;
                    that.smoke_mod = 'show';
                    that.smoke_info = res.data.tape;
                }
            },
            fail: () => {
                uni.showModal({
                    title: '提示',
                    content: '网络繁忙，请稍候重试！',
                    showCancel: false,
                    success: (res) => {}
                });
            }
        });
    },
    pay_type_do() {
        var that = this;
        if (that.contact_person == '') {
            uni.showToast({
                title: '联系方式不能为空',
                icon: 'none',
                duration: 2000
            });
            return;
        }
        if (that.con_index == null) {
            uni.showToast({
                title: '请选择你的星座',
                icon: 'none',
                duration: 2000
            });
            return;
        }
        if (that.hedge_content == '') {
            uni.showToast({
                title: '请填写自我介绍',
                icon: 'none',
                duration: 2000
            });
            return;
        }
        if (this.f_key == 1) {
            //男
            if (this.info['throw_price_male'] > 0) {
                if (that.info['pay_type'] == 2) {
                    that.pay_submit();
                } else {
                    that.do_it();
                }
            } else {
                that.do_it();
            }
        } else {
            if (this.info['throw_price_female'] > 0) {
                if (that.info['pay_type'] == 2) {
                    that.pay_submit();
                } else {
                    that.do_it();
                }
            } else {
                that.do_it();
            }
        }
    },
    do_it(number = 0) {
        var that = this;
        var e = app.globalData.getCache('userinfo');
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        params.contact_person = this.contact_person;
        params.con_index = this.con_index;
        params.area_index = this.area_index;
        params.c_area_index = this.c_area_index;
        params.old_name = this.old_name;
        params.hedge_content = this.hedge_content;
        params.imgList = this.imgList;
        params.life_index = this.life_index;
        params.life_name = this.life_name;
        params.gender = this.f_key;
        params.number = number;
        var b = app.globalData.api_root + 'Tape/do_tape';
        http.POST(b, {
            params: params,
            success: (res) => {
                console.log(res);
                if (res.data.code == 0) {
                    uni.showToast({
                        title: '放入成功！',
                        duration: 1500
                    });
                    setTimeout(() => {
                        that.onLoad();
                    }, 1500);
                } else {
                    uni.showModal({
                        title: '提示',
                        content: res.data.msg,
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            },
            fail: () => {
                uni.showModal({
                    title: '提示',
                    content: '网络繁忙，请稍候重试！',
                    showCancel: false,
                    success: (res) => {}
                });
            }
        });
    },
    contact_person_input(e) {
        this.contact_person = e.detail.value;
    },
    textareaAInput(e) {
        this.hedge_content = e.detail.value;
    },
    /**
     * 删除图片
     */
    DelImg(e) {
        var that = this;
        var index = e.target.dataset['index'];
        var notes = that.imgList;
        notes.splice(index, 1);
        that.imgList = notes;
    },
    /**
     * 上传图片
     */
    chooseImage() {
        var that = this;
        var e = app.globalData.getCache('userinfo');
        var b = app.globalData.api_root + 'User/img_upload';
        uni.chooseImage({
            count: 3,
            sizeType: ['original', 'compressed'],
            // 可以指定是原图还是压缩图，默认二者都有
            sourceType: ['album', 'camera'],
            // 可以指定来源是相册还是相机，默认二者都有
            success: (res) => {
                console.log(res);
                uni.showLoading({
                    title: '上传中...',
                    mask: true
                });
                let tempFilePaths = res.tempFilePaths;
                for (var i = 0, h = tempFilePaths.length; i < h; i++) {
                    uni.uploadFile({
                        url: b,
                        filePath: tempFilePaths[i],
                        name: 'sngpic',
                        header: {
                            'content-type': 'multipart/form-data'
                        },
                        formData: {
                            'content-type': 'multipart/form-data',
                            token: e.token,
                            openid: e.openid,
                            much_id: app.globalData.siteInfo.uniacid
                        },
                        success: (res) => {
                            console.log(res);
                            if (res.data == '') {
                                uni.hideLoading();
                                uni.showModal({
                                    title: '提示',
                                    content: '内存溢出，请稍候重试'
                                });
                                return;
                            }
                            var data = JSON.parse(res.data);
                            console.log(data);
                            if (data.status == 'error') {
                                uni.hideLoading();
                                uni.showModal({
                                    title: '提示',
                                    content: data.msg
                                });
                                return;
                            } else {
                                that.imgList = that.imgList.concat(data.url);
                                uni.hideLoading();
                            }
                        },
                        fail: (res) => {
                            uni.showModal({
                                title: '提示',
                                content: '上传错误！'
                            });
                        }
                    });
                }
            }
        });
    },
    bindPickerChange(e) {
        var index = e.detail.value;
        var id = this.area_array[index].id;
        this.area_index = id;
        this.area_name = this.area_array[index].name;
    },
    bindPickerChangeChouArea(e) {
        var index = e.detail.value;
        var id = this.a_area_array[index].id;
        this.a_area_index = id;
        this.a_area_name = this.a_area_array[index].name;
    },
    bindPickerChangeChou(e) {
        var index = e.detail.value;
        var id = this.c_area_array[index].id;
        this.c_area_index = id;
        this.c_area_name = this.c_area_array[index].name;
    },
    bindPickerChangeLife(e) {
        this.life_index = e.detail.value;
        this.life_name = this.life_array[e.detail.value];
    },
    bindPickerOld(e) {
        this.old_index = e.detail.value;
        this.old_name = this.old_array[e.detail.value];
    },
    PickerChange(e) {
        this.con_index = e.detail.value;
    },
    f_notes(item) {
        var e = app.globalData.getCache('userinfo');
        if (e.tourist == 1) {
            this.check_user_login = true;
            return;
        }
        this.f_key = item.currentTarget.dataset.key;
        this.modal = 'f_modal';
    },
    hideModal() {
        this.modal = null;
        this.smoke_mod = null;
        //this.onLoad();
    },

    doIt(item) {
        var that = this;
        let i = 0;
        numDH();
        function numDH() {
            if (i < 20) {
                setTimeout(function () {
                    that.starCount = i;
                    that.forksCount = i;
                    that.visitTotal = i;
                    i++;
                    numDH();
                }, 20);
            } else {
                console.log(item);
                that.starCount = that.coutNum(item.men);
                that.forksCount = that.coutNum(item.women);
                that.visitTotal = that.coutNum(item.men + item.women);
            }
        }
    },
    coutNum(e) {
        if (e > 1000 && e < 10000) {
            e = (e / 1000).toFixed(1) + 'k';
        }
        if (e > 10000) {
            e = (e / 10000).toFixed(1) + 'W';
        }
        return e;
    },
    pay_smoke_submit() {
        var that = this;
        var e = app.globalData.getCache('userinfo');
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        params.uid = e.uid;
        params.gender = this.c_key;
        var b = app.globalData.api_root + 'Pay/tape_smoke_pay';
        http.POST(b, {
            params: params,
            success: function (res) {
                console.log(res);
                if (res.data.code == 1) {
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: res.data.msg,
                        showCancel: false,
                        success: function (res) {}
                    });
                    return;
                }
                if (res.data.return_msg == 'OK') {
                    var timeStamp = (Date.parse(new Date()) / 1000).toString();
                    var pkg = 'prepay_id=' + res.data.prepay_id;
                    var nonceStr = res.data.nonce_str;
                    var paySign = md5
                        .hexMD5(
                            'appId=' +
                                res.data.appid +
                                '&nonceStr=' +
                                nonceStr +
                                '&package=' +
                                pkg +
                                '&signType=MD5&timeStamp=' +
                                timeStamp +
                                '&key=' +
                                res.data.app_info['app_key']
                        )
                        .toUpperCase(); //此处用到hexMD5插件
                    //发起支付
                    uni.requestPayment({
                        timeStamp: timeStamp,
                        nonceStr: nonceStr,
                        package: pkg,
                        signType: 'MD5',
                        paySign: paySign,
                        success: function () {
                            uni.hideLoading();
                            that.do_smoke_it(res.data.number);
                        },
                        fail: function (ras) {
                            uni.hideLoading();
                        }
                    });
                } else {
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: '支付参数错误！！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            },
            fail: function () {
                uni.hideLoading();
                uni.showModal({
                    title: '提示',
                    content: '网络繁忙，请稍候重试！',
                    showCancel: false,
                    success: function (res) {}
                });
            }
        });
    },
    /**
     * 支付
     */
    pay_submit() {
        var that = this;
        var e = app.globalData.getCache('userinfo');
        var params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        params.uid = e.uid;
        params.f_key = this.f_key;
        var b = app.globalData.api_root + 'Pay/tape_pay';
        http.POST(b, {
            params: params,
            success: function (res) {
                console.log(res);
                if (res.data.return_msg == 'OK') {
                    var timeStamp = (Date.parse(new Date()) / 1000).toString();
                    var pkg = 'prepay_id=' + res.data.prepay_id;
                    var nonceStr = res.data.nonce_str;
                    var paySign = md5
                        .hexMD5(
                            'appId=' +
                                res.data.appid +
                                '&nonceStr=' +
                                nonceStr +
                                '&package=' +
                                pkg +
                                '&signType=MD5&timeStamp=' +
                                timeStamp +
                                '&key=' +
                                res.data.app_info['app_key']
                        )
                        .toUpperCase(); //此处用到hexMD5插件
                    //发起支付
                    uni.requestPayment({
                        timeStamp: timeStamp,
                        nonceStr: nonceStr,
                        package: pkg,
                        signType: 'MD5',
                        paySign: paySign,
                        success: function () {
                            that.do_it(res.data.number);
                        },
                        complete: function () {
                            // wx.showToast({
                            //   title: '支付失败',
                            //   icon: 'none',
                            //   duration: 2000
                            // })
                        }
                    });
                } else {
                    uni.showToast({
                        title: '参数错误！',
                        icon: 'none',
                        duration: 2000
                    });
                    //that.get_pay();
                }
            },

            fail: function () {
                uni.showModal({
                    title: '提示',
                    content: '网络繁忙，请稍候重试！',
                    showCancel: false,
                    success: function (res) {}
                });
            }
        });
    },
    /**
     * 一键复制
     */
    copyBtn(e) {
        var that = this;
        uni.setClipboardData({
            data: e.currentTarget.dataset.key,
            success: function (res) {
                console.log(res);
            }
        });
    },
    /**
     * 图片预览
     */
    previewImage(e) {
        var index = e.currentTarget.dataset.key;
        var current = this.smoke_info['image_part'][index];
        uni.previewImage({
            current: current,
            // 当前显示图片的http链接
            urls: this.smoke_info['image_part'] // 需要预览的图片http链接列表
        });
    },

    }
};
</script>
<style></style>
