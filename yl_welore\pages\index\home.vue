<template>
    <view>
        <view v-if="info_home.length > 0" style="width: 100%">
            <swiper
                class="round-dot"
                :indicator-dots="true"
                :style="'height:' + (info_home[0].length > 4 ? 400 : 220) + 'rpx;'"
                indicator-active-color="#696969"
                indicator-color="rgba(150, 150, 150, 0.8)"
            >
                <block v-for="(one, e_index) in info_home" :key="e_index">
                    <swiper-item>
                        <view class="grid col-4 text-center" style="padding-top: 10px; width: 100%">
                            <view
                                @tap="set_one"
                                :data-type="n_item.practice_type"
                                :data-path="n_item.wx_app_url"
                                :data-url="n_item.url"
                                :data-key="e_index"
                                :data-index="n_index"
                                style="margin: 5px 0px"
                                v-for="(n_item, n_index) in one"
                                :key="n_index"
                            >
                                <image :lazy-load="true" :src="n_item.playbill_url" style="width: 90rpx; height: 90rpx"></image>

                                <view v-if="n_item.playbill_name" class="botton_text">
                                    {{ n_item.playbill_name }}
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </block>
            </swiper>
        </view>
        <block v-if="put_top_list.length > 0 && put_top_info.display_switch == 1">
            <view v-if="put_top_info.style_type == 0" @tap="top_url" class="text-center" style="position: relative">
                <view class="text-bold" style="color: #ffffff; position: absolute; z-index: 10; font-size: 36rpx; width: 94rpx; left: 56rpx; top: 24rpx; letter-spacing: 6rpx">
                    {{ put_top_info.custom_title }}
                </view>
                <view style="width: 75%; color: #000000; padding-right: 30rpx; position: absolute; z-index: 10; left: 170rpx; top: 30rpx">
                    <swiper easing-function="linear" autoplay interval="5000" circular vertical style="height: 90rpx">
                        <swiper-item v-for="(item, index) in put_top_list" :key="index">
                            <view class="text_num_1 text-bold text-left" style="width: 100%">
                                <image
                                    style="width: 25rpx; height: 25rpx; vertical-align: middle"
                                    :src="http_root + 'addons/yl_welore/web/static/examine/' + (index + 1) + '.png'"
                                ></image>
                                <text style="letter-spacing: 2rpx; font-size: 30rpx; margin-left: 10rpx; vertical-align: middle">
                                    {{ item.study_title == '' ? item.study_content : item.study_title }}
                                </text>
                            </view>

                            <view class="text-right" style="letter-spacing: 3rpx; font-size: 24rpx; margin-top: 20rpx">围观数{{ item.hort }}</view>
                        </swiper-item>
                    </swiper>
                </view>
                <image class="now_level" style="width: 95%; border-radius: 20rpx" mode="widthFix" src="/static/yl_welore/style/icon/sign_top_bac.png"></image>
            </view>
            <view v-if="put_top_info.style_type == 1" @tap="top_url" class="text-center" style="position: relative">
                <view style="width: 75%; color: #000000; padding-right: 30rpx; position: absolute; z-index: 10; left: 170rpx; top: 0rpx">
                    <swiper easing-function="linear" autoplay interval="5000" circular vertical style="height: 120rpx; padding-top: 15rpx">
                        <swiper-item v-for="(item, index) in put_top_list" :key="index">
                            <view class="text_num_1 text-bold text-left" style="width: 100%">
                                <image
                                    style="width: 25rpx; height: 25rpx; vertical-align: middle"
                                    :src="http_root + 'addons/yl_welore/web/static/examine/' + (index + 1) + '.png'"
                                ></image>
                                <text style="letter-spacing: 2rpx; font-size: 30rpx; margin-left: 10rpx; vertical-align: middle">
                                    {{ item.study_title == '' ? item.study_content : item.study_title }}
                                </text>
                            </view>

                            <view class="text-right" style="letter-spacing: 3rpx; font-size: 24rpx; margin-top: 25rpx">围观数{{ item.hort }}</view>
                        </swiper-item>
                    </swiper>
                </view>
                <image class="now_level" style="width: 95%; border-radius: 20rpx" mode="widthFix" src="/static/yl_welore/style/icon/sign_top_bac1.png"></image>
            </view>
        </block>

        <view v-if="copyright.home_my_tory_arbor == 1" class="my-realm-container">
            <!-- 头部标题区域 -->
            <view class="realm-header" :style="'margin-top: ' + (info_home.length == 0 ? 20 : 0) + 'px;'">
                <view class="realm-title">我的{{ $state.diy.landgrave }}</view>
                <view class="realm-more-btn" @tap="get_all_qq">
                    <text class="more-text">全部</text>
                    <text class="cicon-angle more-icon"></text>
                </view>
            </view>

            <!-- 滚动卡片区域 -->
            <scroll-view :scroll-x="true" @scrolltolower="nex_my_qq" class="realm-scroll-container">
                <view
                    :data-id="item.id"
                    @tap="this_url"
                    class="realm-card"
                    v-for="(item, index) in my_qq_list"
                    :key="index"
                >
                    <!-- 头像容器 -->
                    <view class="realm-avatar-container">
                        <view class="realm-avatar" :style="'background-image:url(' + item.realm_icon + ');'"></view>
                    </view>

                    <!-- 名称文字 -->
                    <view class="realm-name">
                        <text class="text-cut">{{ item.realm_name }}</text>
                    </view>
                </view>

                <!-- 空状态 -->
                <view v-if="my_qq_list.length == 0" @tap="get_all_qq" class="realm-empty-state">
                    <view class="empty-content">
                        <text class="cicon-discover empty-icon"></text>
                        <text class="empty-text">查看更多{{ $state.diy.landgrave }}</text>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        info_home() {
            return (this.data && this.data.info_home) || [];
        },
        put_top_list() {
            return (this.data && this.data.put_top_list) || [];
        },
        put_top_info() {
            return (this.data && this.data.put_top_info) || {};
        },
        http_root() {
            return this.data && this.data.http_root;
        },
        copyright() {
            return (this.data && this.data.copyright) || {};
        },
       
        my_qq_list() {
            return (this.data && this.data.my_qq_list) || [];
        },
        
    },
    methods: {
        bindchange_top(e) {
            this.$emit('bindchange_top', e);
        },
        set_one(e) {
           this.$emit('set_one', e);
        },
        top_url(e) {
            this.$emit('top_home_url', e);
        },
        get_all_qq(e) {
            this.$emit('get_all_qq', e);
        },
        nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url',e);
        }
    }
};
</script>
<style scoped>
/* 我的XX模块样式优化 */
.my-realm-container {
    margin: 20rpx 20rpx 30rpx 20rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    border-radius: 24rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 8rpx 32rpx rgba(102, 126, 234, 0.15),
        0 4rpx 16rpx rgba(118, 75, 162, 0.1),
        0 2rpx 8rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: relative;
}

/* 整体卡片的装饰效果 */
.my-realm-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2rpx;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.8) 20%,
        rgba(255, 255, 255, 0.9) 50%,
        rgba(255, 255, 255, 0.8) 80%,
        transparent);
    z-index: 2;
}

/* 背景装饰纹理 */
.my-realm-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

/* 头部标题区域 */
.realm-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 30rpx 20rpx 30rpx;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.85) 50%,
        rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(10rpx);
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 3;
}

.realm-title {
    font-size: 34rpx;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1rpx;
    text-shadow: 0 2rpx 4rpx rgba(102, 126, 234, 0.1);
}

.realm-more-btn {
    display: flex;
    align-items: center;
    padding: 10rpx 20rpx;
    border-radius: 25rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%);
    border: 1rpx solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10rpx);
}

.realm-more-btn:active {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    transform: scale(0.95);
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
}

.more-text {
    font-size: 26rpx;
    color: #667eea;
    margin-right: 6rpx;
    font-weight: 500;
}

.more-icon {
    font-size: 24rpx;
    color: #667eea;
    transition: all 0.3s ease;
}

.realm-more-btn:active .more-icon {
    transform: translateX(3rpx);
    color: #764ba2;
}

/* 滚动容器 */
.realm-scroll-container {
    white-space: nowrap;
    width: 100%;
    padding: 20rpx 20rpx 30rpx 20rpx;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.9) 100%);
    backdrop-filter: blur(10rpx);
    position: relative;
    z-index: 2;
}

/* 内部小卡片样式 */
.realm-card {
    display: inline-block;
    width: 160rpx;
    margin: 0 12rpx;
    padding: 24rpx 16rpx;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.7) 100%);
    border-radius: 16rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.5);
    box-shadow:
        0 4rpx 12rpx rgba(0, 0, 0, 0.08),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
    text-align: center;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    backdrop-filter: blur(10rpx);
}

/* 为每个卡片添加不同的彩色边框 */
.realm-card:nth-child(4n+1) {
    border-left: 3rpx solid #ff6b6b;
    box-shadow:
        0 4rpx 12rpx rgba(255, 107, 107, 0.15),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.realm-card:nth-child(4n+2) {
    border-left: 3rpx solid #4ecdc4;
    box-shadow:
        0 4rpx 12rpx rgba(78, 205, 196, 0.15),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.realm-card:nth-child(4n+3) {
    border-left: 3rpx solid #45b7d1;
    box-shadow:
        0 4rpx 12rpx rgba(69, 183, 209, 0.15),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.realm-card:nth-child(4n+4) {
    border-left: 3rpx solid #f7b731;
    box-shadow:
        0 4rpx 12rpx rgba(247, 183, 49, 0.15),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
}

.realm-card:active {
    transform: translateY(-3rpx) scale(0.98);
    box-shadow:
        0 8rpx 20rpx rgba(0, 0, 0, 0.12),
        0 4rpx 8rpx rgba(0, 0, 0, 0.08),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

/* 卡片内部高光效果 */
.realm-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
    opacity: 0.6;
}

/* 卡片底部微妙阴影 */
.realm-card::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 8rpx;
    right: 8rpx;
    height: 1rpx;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
    opacity: 0.3;
}

/* 头像容器 */
.realm-avatar-container {
    margin-bottom: 20rpx;
    position: relative;
    display: flex;
    justify-content: center;
}

.realm-avatar {
    width: 88rpx;
    height: 88rpx;
    border-radius: 16rpx;
    background-color: #f8f9fa;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: 3rpx solid rgba(255, 255, 255, 0.9);
    box-shadow:
        0 4rpx 12rpx rgba(0, 0, 0, 0.1),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

/* 头像彩色光晕效果 */
.realm-card:nth-child(4n+1) .realm-avatar {
    box-shadow:
        0 4rpx 12rpx rgba(255, 107, 107, 0.2),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        0 0 0 2rpx rgba(255, 107, 107, 0.1),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.realm-card:nth-child(4n+2) .realm-avatar {
    box-shadow:
        0 4rpx 12rpx rgba(78, 205, 196, 0.2),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        0 0 0 2rpx rgba(78, 205, 196, 0.1),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.realm-card:nth-child(4n+3) .realm-avatar {
    box-shadow:
        0 4rpx 12rpx rgba(69, 183, 209, 0.2),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        0 0 0 2rpx rgba(69, 183, 209, 0.1),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

.realm-card:nth-child(4n+4) .realm-avatar {
    box-shadow:
        0 4rpx 12rpx rgba(247, 183, 49, 0.2),
        0 2rpx 6rpx rgba(0, 0, 0, 0.06),
        0 0 0 2rpx rgba(247, 183, 49, 0.1),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.6);
}

/* 头像内部高光 */
.realm-avatar::before {
    content: '';
    position: absolute;
    top: 3rpx;
    left: 3rpx;
    right: 3rpx;
    height: 24rpx;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.4), transparent);
    border-radius: 12rpx 12rpx 50rpx 50rpx;
    pointer-events: none;
}

.realm-card:active .realm-avatar {
    transform: scale(1.05);
    box-shadow:
        0 6rpx 16rpx rgba(0, 0, 0, 0.15),
        0 3rpx 8rpx rgba(0, 0, 0, 0.08),
        0 0 0 3rpx rgba(102, 126, 234, 0.2),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.7);
}

/* 名称文字 */
.realm-name {
    font-size: 26rpx;
    color: #333333;
    line-height: 1.3;
    font-weight: 500;
    padding: 0 4rpx;
}

.realm-name .text-cut {
    display: block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

/* 空状态样式 */
.realm-empty-state {
    display: inline-block;
    width: 100%;
    padding: 20rpx;
    text-align: center;
}

.empty-content {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 50rpx;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.8) 0%,
        rgba(248, 249, 250, 0.9) 100%);
    border-radius: 16rpx;
    border: 2rpx dashed rgba(102, 126, 234, 0.3);
    box-shadow:
        0 4rpx 12rpx rgba(102, 126, 234, 0.08),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    backdrop-filter: blur(10rpx);
}

.empty-content:active {
    transform: scale(0.98);
    border-color: rgba(118, 75, 162, 0.5);
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.1) 0%,
        rgba(255, 255, 255, 0.9) 100%);
    box-shadow:
        0 6rpx 16rpx rgba(118, 75, 162, 0.12),
        inset 0 1rpx 0 rgba(255, 255, 255, 0.9);
}

.empty-icon {
    font-size: 36rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-right: 10rpx;
    animation: pulse 2s ease-in-out infinite;
}

.empty-text {
    font-size: 28rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 600;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 响应式优化 */
@media screen and (max-width: 750rpx) {
    .realm-card {
        width: 140rpx;
        margin: 0 8rpx;
        padding: 20rpx 12rpx;
    }

    .realm-avatar {
        width: 76rpx;
        height: 76rpx;
    }

    .realm-name {
        font-size: 24rpx;
    }
}
</style>
