<template>
    <view>
        <view v-if="info_home.length > 0" style="width: 100%">
            <swiper
                class="round-dot"
                :indicator-dots="true"
                :style="'height:' + (info_home[0].length > 4 ? 400 : 220) + 'rpx;'"
                indicator-active-color="#696969"
                indicator-color="rgba(150, 150, 150, 0.8)"
            >
                <block v-for="(one, e_index) in info_home" :key="e_index">
                    <swiper-item>
                        <view class="grid col-4 text-center" style="padding-top: 10px; width: 100%">
                            <view
                                @tap="set_one"
                                :data-type="n_item.practice_type"
                                :data-path="n_item.wx_app_url"
                                :data-url="n_item.url"
                                :data-key="e_index"
                                :data-index="n_index"
                                style="margin: 5px 0px"
                                v-for="(n_item, n_index) in one"
                                :key="n_index"
                            >
                                <image :lazy-load="true" :src="n_item.playbill_url" style="width: 90rpx; height: 90rpx"></image>

                                <view v-if="n_item.playbill_name" class="botton_text">
                                    {{ n_item.playbill_name }}
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </block>
            </swiper>
        </view>
        <block v-if="put_top_list.length > 0 && put_top_info.display_switch == 1">
            <view v-if="put_top_info.style_type == 0" @tap="top_url" class="text-center" style="position: relative">
                <view class="text-bold" style="color: #ffffff; position: absolute; z-index: 10; font-size: 36rpx; width: 94rpx; left: 56rpx; top: 24rpx; letter-spacing: 6rpx">
                    {{ put_top_info.custom_title }}
                </view>
                <view style="width: 75%; color: #000000; padding-right: 30rpx; position: absolute; z-index: 10; left: 170rpx; top: 30rpx">
                    <swiper easing-function="linear" autoplay interval="5000" circular vertical style="height: 90rpx">
                        <swiper-item v-for="(item, index) in put_top_list" :key="index">
                            <view class="text_num_1 text-bold text-left" style="width: 100%">
                                <image
                                    style="width: 25rpx; height: 25rpx; vertical-align: middle"
                                    :src="http_root + 'addons/yl_welore/web/static/examine/' + (index + 1) + '.png'"
                                ></image>
                                <text style="letter-spacing: 2rpx; font-size: 30rpx; margin-left: 10rpx; vertical-align: middle">
                                    {{ item.study_title == '' ? item.study_content : item.study_title }}
                                </text>
                            </view>

                            <view class="text-right" style="letter-spacing: 3rpx; font-size: 24rpx; margin-top: 20rpx">围观数{{ item.hort }}</view>
                        </swiper-item>
                    </swiper>
                </view>
                <image class="now_level" style="width: 95%; border-radius: 20rpx" mode="widthFix" src="/static/yl_welore/style/icon/sign_top_bac.png"></image>
            </view>
            <view v-if="put_top_info.style_type == 1" @tap="top_url" class="text-center" style="position: relative">
                <view style="width: 75%; color: #000000; padding-right: 30rpx; position: absolute; z-index: 10; left: 170rpx; top: 0rpx">
                    <swiper easing-function="linear" autoplay interval="5000" circular vertical style="height: 120rpx; padding-top: 15rpx">
                        <swiper-item v-for="(item, index) in put_top_list" :key="index">
                            <view class="text_num_1 text-bold text-left" style="width: 100%">
                                <image
                                    style="width: 25rpx; height: 25rpx; vertical-align: middle"
                                    :src="http_root + 'addons/yl_welore/web/static/examine/' + (index + 1) + '.png'"
                                ></image>
                                <text style="letter-spacing: 2rpx; font-size: 30rpx; margin-left: 10rpx; vertical-align: middle">
                                    {{ item.study_title == '' ? item.study_content : item.study_title }}
                                </text>
                            </view>

                            <view class="text-right" style="letter-spacing: 3rpx; font-size: 24rpx; margin-top: 25rpx">围观数{{ item.hort }}</view>
                        </swiper-item>
                    </swiper>
                </view>
                <image class="now_level" style="width: 95%; border-radius: 20rpx" mode="widthFix" src="/static/yl_welore/style/icon/sign_top_bac1.png"></image>
            </view>
        </block>

        <view v-if="copyright.home_my_tory_arbor == 1" class="my-realm-container">
            <!-- 头部标题区域 -->
            <view class="realm-header" :style="'margin-top: ' + (info_home.length == 0 ? 20 : 0) + 'px;'">
                <view class="realm-title">我的{{ $state.diy.landgrave }}</view>
                <view class="realm-more-btn" @tap="get_all_qq">
                    <text class="more-text">全部</text>
                    <text class="cicon-angle more-icon"></text>
                </view>
            </view>

            <!-- 滚动卡片区域 -->
            <scroll-view :scroll-x="true" @scrolltolower="nex_my_qq" class="realm-scroll-container">
                <view
                    :data-id="item.id"
                    @tap="this_url"
                    class="realm-card"
                    v-for="(item, index) in my_qq_list"
                    :key="index"
                >
                    <!-- 头像容器 -->
                    <view class="realm-avatar-container">
                        <view class="realm-avatar" :style="'background-image:url(' + item.realm_icon + ');'"></view>
                    </view>

                    <!-- 名称文字 -->
                    <view class="realm-name">
                        <text class="text-cut">{{ item.realm_name }}</text>
                    </view>
                </view>

                <!-- 空状态 -->
                <view v-if="my_qq_list.length == 0" @tap="get_all_qq" class="realm-empty-state">
                    <view class="empty-content">
                        <text class="cicon-discover empty-icon"></text>
                        <text class="empty-text">查看更多{{ $state.diy.landgrave }}</text>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        info_home() {
            return (this.data && this.data.info_home) || [];
        },
        put_top_list() {
            return (this.data && this.data.put_top_list) || [];
        },
        put_top_info() {
            return (this.data && this.data.put_top_info) || {};
        },
        http_root() {
            return this.data && this.data.http_root;
        },
        copyright() {
            return (this.data && this.data.copyright) || {};
        },
       
        my_qq_list() {
            return (this.data && this.data.my_qq_list) || [];
        },
        
    },
    methods: {
        bindchange_top(e) {
            this.$emit('bindchange_top', e);
        },
        set_one(e) {
           this.$emit('set_one', e);
        },
        top_url(e) {
            this.$emit('top_home_url', e);
        },
        get_all_qq(e) {
            this.$emit('get_all_qq', e);
        },
        nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url',e);
        }
    }
};
</script>
<style scoped>
/* 我的XX模块样式优化 */
.my-realm-container {
    margin: 20rpx 20rpx 30rpx 20rpx;
    background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
    border-radius: 16rpx;
    border: 1rpx solid #e8f2ff;
    box-shadow: 0 2rpx 12rpx rgba(59, 130, 246, 0.08);
    overflow: hidden;
    position: relative;
}



/* 头部标题区域 */
.realm-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 24rpx 16rpx 24rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f5f5f5;
    position: relative;
}

.realm-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #1e40af;
    letter-spacing: 0.5rpx;
}

.realm-more-btn {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    transition: all 0.2s ease;
}

.realm-more-btn:active {
    background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
    transform: scale(0.98);
}

.more-text {
    font-size: 24rpx;
    color: #ffffff;
    margin-right: 4rpx;
    font-weight: 500;
}

.more-icon {
    font-size: 22rpx;
    color: #ffffff;
    transition: all 0.2s ease;
}

/* 滚动容器 */
.realm-scroll-container {
    white-space: nowrap;
    width: 100%;
    padding: 16rpx 16rpx 24rpx 16rpx;
    background: #ffffff;
    position: relative;
}

/* 内部小卡片样式 */
.realm-card {
    display: inline-block;
    width: 160rpx;
    margin: 0 8rpx;
    padding: 20rpx 12rpx;
    background: #ffffff;
    border-radius: 12rpx;
    border: 1rpx solid #f1f5f9;
    box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.06);
    text-align: center;
    position: relative;
    transition: all 0.2s ease;
    overflow: hidden;
}

/* 为卡片添加微妙的颜色变化 */
.realm-card:nth-child(4n+1) {
    border-left: 3rpx solid #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
}

.realm-card:nth-child(4n+2) {
    border-left: 3rpx solid #10b981;
    background: linear-gradient(135deg, #f0fdf4 0%, #ffffff 100%);
}

.realm-card:nth-child(4n+3) {
    border-left: 3rpx solid #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

.realm-card:nth-child(4n+4) {
    border-left: 3rpx solid #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #ffffff 100%);
}



.realm-card:active {
    transform: translateY(-2rpx) scale(0.98);
    box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.15);
}



/* 头像容器 */
.realm-avatar-container {
    margin-bottom: 20rpx;
    position: relative;
    display: flex;
    justify-content: center;
}

.realm-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 12rpx;
    background-color: #f8f9fa;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
    position: relative;
}





.realm-card:active .realm-avatar {
    transform: scale(1.02);
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.12);
}

/* 名称文字 */
.realm-name {
    font-size: 26rpx;
    color: #333333;
    line-height: 1.3;
    font-weight: 500;
    padding: 0 4rpx;
}

.realm-name .text-cut {
    display: block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
}

/* 空状态样式 */
.realm-empty-state {
    display: inline-block;
    width: 100%;
    padding: 20rpx;
    text-align: center;
}

.empty-content {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 50rpx;
    background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
    border-radius: 20rpx;
    border: 2rpx dashed #3b82f6;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.empty-content::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.05), transparent);
    animation: shimmer 3s ease-in-out infinite;
}

.empty-content:active {
    transform: scale(0.98);
    background: #e9ecef;
}

.empty-icon {
    font-size: 32rpx;
    color: #999999;
    margin-right: 8rpx;
}

.empty-text {
    font-size: 26rpx;
    color: #999999;
    font-weight: 500;
}



/* 响应式优化 */
@media screen and (max-width: 750rpx) {
    .realm-card {
        width: 140rpx;
        margin: 0 6rpx;
        padding: 16rpx 10rpx;
    }

    .realm-avatar {
        width: 72rpx;
        height: 72rpx;
    }

    .realm-name {
        font-size: 24rpx;
    }
}
</style>
