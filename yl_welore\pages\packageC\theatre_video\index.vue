<template>
    <view>
        <cu-custom v-if="fullscreen != 'vertical'" bgColor="transparent" :isSearch="false" :isBack="true" style="color: #fff">
            <view slot="backText">{{ videoList[current].msi_episode_number }}</view>
        </cu-custom>
        <!-- <view catchtap="no_quan" wx:if="{{fullscreen=='horizontal'}}" style="font-size: 14px;color: #ffffff;position: fixed;z-index: 9999999999;left:30rpx;top: 30rpx;">
    <text class="cicon-back"></text>
    <text>{{videoList[current].msi_episode_number}}</text>
</view> -->
        <swiper @animationfinish="changeSwiper" :current="current" :circular="circular" vertical>
            <block v-for="(item, index) in videoList" :key="index">
                <swiper-item style="position: relative">
                    <block v-if="!select">
                        <view style="height: 120rpx; color: #ffffff; background-color: rgba(0, 0, 0, 1); position: absolute; bottom: 0rpx; z-index: 1; width: 100%; padding: 20rpx">
                            <view class="flex justify-between align-center" style="letter-spacing: 2rpx">
                                <view class="text_num_1" style="width: 500rpx; color: #cfcfcf; font-weight: 600; font-size: 30rpx">
                                    <text style="vertical-align: middle">{{ info.title }}</text>
                                    <text style="margin: 0rpx 10rpx; vertical-align: middle">-</text>
                                    <text style="vertical-align: middle">共{{ info.total_episodes }}集</text>
                                </view>
                                <view class="flex align-center" style="color: #939393">
                                    <view @tap.stop.prevent="open_bs">{{ backRateIndex == 0 ? '倍速' : backRate[backRateIndex] + '倍' }}</view>
                                    <button @tap.stop.prevent="open_mode" class="cu-btn round lines-white sm" style="color: #939393; margin-left: 35rpx">选集</button>
                                </view>
                            </view>
                        </view>
                        <view v-if="lock != -1" style="z-index: 1; position: absolute; text-align: center; right: 30rpx; bottom: 15%; color: #ffffff">
                            <view v-if="lock != 0 && lock != 3" @tap="showPay" style="margin-bottom: 60rpx">
                                <view><text class="cicon-lock" style="font-size: 50rpx"></text></view>
                                <view style="font-size: 22rpx; letter-spacing: 1rpx">解锁</view>
                            </view>
                            <view @tap="like" :data-type="item.is_zj" data-key="0" :data-index="index">
                                <view>
                                    <text v-if="item.is_xh == 0" class="cicon-favorite-o" style="font-size: 50rpx"></text>
                                    <text v-if="item.is_xh > 0" class="cicon-favorite text-red" style="font-size: 50rpx"></text>
                                </view>
                            </view>
                            <view @tap="ping" :data-index="index" style="margin-top: 60rpx">
                                <view>
                                    <!-- <text class="cicon-chat-smiles" style="font-size:50rpx;"></text> -->
                                    <image src="/static/yl_welore/style/icon/theatre/ping.png" style="width: 50rpx" mode="widthFix" class="now_level"></image>
                                </view>
                                <view style="font-size: 22rpx; letter-spacing: 1rpx">{{ RrplyListCount }}</view>
                            </view>
                            <!-- <view bind:tap="like" data-type="{{item.is_zj}}" data-key="1" data-index="{{index}}" style="margin:60rpx 0rpx">
                        <view><text class="cicon-book {{item.is_zj>0?'text-yellow':''}}" style="font-size:50rpx;"></text></view>
                        <view style="font-size: 22rpx;letter-spacing: 1rpx;">追剧</view>
                    </view> -->
                            <view style="margin-top: 60rpx">
                                <button style="font-size: 40rpx" hover-class="none" open-type="share" class="text-white">
                                    <image src="/static/yl_welore/style/icon/theatre/fen.png" style="width: 45rpx" mode="widthFix" class="now_level"></image>
                                </button>
                                <!-- <view style="font-size: 22rpx;letter-spacing: 1rpx;">分享</view> -->
                            </view>
                            <view style="margin-top: 60rpx">
                                <view @tap="quan">
                                    <image src="/static/yl_welore/style/icon/theatre/quan.png" style="width: 50rpx" mode="widthFix" class="now_level"></image>
                                </view>
                            </view>
                            <view style="margin-top: 60rpx">
                                <view @tap="tou">
                                    <image src="/static/yl_welore/style/icon/theatre/tou.png" style="width: 50rpx" mode="widthFix" class="now_level"></image>
                                </view>
                            </view>
                        </view>
                    </block>
                    <block v-if="current == index">
                        <video
                            :show-casting-button="true"
                            :initial-time="initial_time"
                            :custom-cache="false"
                            @seekcomplete="addbindseekcomplete"
                            :poster="info.poster_url"
                            :animation="current == index ? animBack : ''"
                            id="myVideo"
                            :src="item.msi_episode_url"
                            :controls="controls"
                            object-fit="contain"
                            :enable-danmu="true"
                            :loop="true"
                            :danmu-btn="false"
                            @waiting="videoWaiting"
                            @timeupdate="timeupdate"
                            @loadedmetadata="loadedmetadata"
                            @progress="progress"
                            @play="eventPlay"
                            :show-progress="true"
                            :show-fullscreen-btn="false"
                            :show-play-btn="true"
                            :autoplay="true"
                            :show-center-play-btn="true"
                            :enable-auto-rotation="true"
                            :show-screen-lock-button="true"
                            @ended="endNo"
                            :enable-progress-gesture="true"
                            @fullscreenchange="quanOp"
                            play-btn-position="center"
                            :show-snapshot-button="true"
                            @tap.stop.prevent="playOrPause"
                        >
                            <view v-if="controls" style="position: fixed; right: 80rpx; bottom: 80rpx; font-size: 15px; color: #ffffff">
                                <view @tap.stop.prevent="open_bs">{{ backRateIndex == 0 ? '倍速' : backRate[backRateIndex] + '倍' }}</view>
                            </view>
                        </video>
                        <view class="progress-box">
                            <progress :percent="percent" stroke-width="2" backgroundColor="#3E3E3E" activeColor="#FFFFFF" />
                        </view>
                    </block>
                    <!-- 暂停 -->
                    <view v-if="!isPlaying && fullscreen == 'vertical'" :data-index="index" @tap.stop.prevent="playOrPause">
                        <image src="/static/yl_welore/style/icon/d_bf.png" class="pause"></image>
                    </view>
                </swiper-item>
            </block>
        </swiper>
        <view :class="'cu-modal bottom-modal ' + (select ? 'show' : '')" @tap="hideModal" style="background: transparent; border-radius: 20rpx">
            <view class="cu-dialog" style="height: 50%">
                <view class="cu-bar bg-white" style="padding: 0rpx 20rpx">
                    <view style="color: #000000">{{ info.title }}-共{{ info.total_episodes }}集</view>
                    <view style="color: #0099ff" @tap.stop.prevent="openInfo">
                        <text>详情</text>
                        <text class="cuIcon-right"></text>
                    </view>
                </view>
                <scroll-view scroll-y style="height: 680rpx; padding: 20rpx">
                    <view class="flex justify-start" style="flex-wrap: wrap">
                        <view
                            @tap.stop.prevent="openEpis"
                            :data-index="index"
                            :data-id="item.id"
                            :class="'shadow ' + (item.id == epis ? 'ss' : 'jj')"
                            v-for="(item, index) in videoList"
                            :key="index"
                        >
                            <text style="font-size: 24rpx">{{ item.msi_episode_number_name }}</text>

                            <image
                                v-if="item.is_allow_only_vip == 1"
                                src="/static/yl_welore/style/icon/bot_vip.png"
                                style="width: 40rpx; height: 40rpx; position: absolute; right: 1px; bottom: 1px"
                            ></image>

                            <image
                                v-if="item.is_allow_only_vip == 0 && item.paid_unlocking_type != 0"
                                src="/static/yl_welore/style/icon/bot_suo.png"
                                style="width: 40rpx; height: 40rpx; position: absolute; right: 1px; bottom: 1px"
                            ></image>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>
        <view v-if="modalName" class="cu-modal bottom-modal show" @tap="hideModalPl">
            <view class="cu-dialog" style="height: 60%; border-radius: 10px 10px 0px 0px" @tap.stop.prevent="t">
                <view class="cu-bar bg-white">
                    <view class="action text-green" style="margin-left: 0px"></view>
                    <view class="action text-black">{{ RrplyListCount }}条评论</view>
                    <view class="action text-black cicon-close" @tap="hideModalPl" style="font-size: 20px"></view>
                </view>
                <scroll-view :scroll-y="true" @scrolltolower="RrplyListNext" class="padding-sl bg-white" style="height: 90%; padding-bottom: 132rpx">
                    <view class="cu-list menu-avatar comment">
                        <view class="cu-item" v-for="(item, r_index) in RrplyList" :key="r_index">
                            <view class="cu-avatar round" :style="'background-image:url(' + item.user_head_sculpture + ');'"></view>

                            <view class="content" style="width: calc(100% - 96rpx - 60rpx - 120rpx - 20rpx)">
                                <view style="color: #7b7d82; font-size: 12px">{{ item.user_nick_name }}</view>
                                <view class="text-black text-content text-df text-left" style="width: 86%; word-wrap: break-word; word-break: normal">
                                    <rich-text :nodes="item.comment"></rich-text>
                                </view>
                                <view class="margin-top-sm flex justify-between" :data-id="item.id" :data-key="r_index">
                                    <view style="color: #76777d; font-size: 12px">
                                        <text>{{ item.create_time }}</text>
                                        <text class="cicon-title" style="font-size: 20rpx; margin: 0 10rpx"></text>
                                        <text @tap.stop.prevent="AddReply">回复</text>
                                        <block v-if="u.id == item.user_id">
                                            <text class="cicon-title" style="font-size: 20rpx; margin: 0 10rpx"></text>
                                            <text>删除</text>
                                        </block>
                                    </view>
                                </view>
                                <view class="cu-list menu-avatar comment children" :style="'max-height: ' + item.expandList.length * 100 + 'px;'">
                                    <view class="cu-item" style="padding: 30rpx 30rpx 0rpx 70rpx" v-for="(ex, e_index) in item.expandList" :key="e_index">
                                        <view class="cu-avatar round sm" :style="'background-image:url(' + ex.reply_user.user_head_sculpture + ');left: 0;'"></view>

                                        <view class="content">
                                            <view class="text_num_1" style="color: #7b7d82; font-size: 12px">
                                                <text>{{ ex.reply_user.user_nick_name }}</text>
                                            </view>
                                            <view class="text-black text-content text-df text-left" style="width: 86%">
                                                <rich-text :nodes="ex.comment"></rich-text>
                                            </view>
                                            <view class="margin-top-sm flex justify-between">
                                                <view style="color: #76777d; font-size: 12px">
                                                    <text>{{ ex.create_time }}</text>
                                                </view>
                                            </view>
                                        </view>
                                    </view>
                                </view>
                                <view
                                    v-if="item.hui_count > 0"
                                    @tap.stop.prevent="combination"
                                    :data-id="item.id"
                                    :data-key="r_index"
                                    class="text-left"
                                    style="margin-top: 10px; color: #8a8b90"
                                >
                                    <text class="cicon-move"></text>
                                    <text v-if="item.expandList.length == 0">展开{{ item.hui_count }}条回复</text>
                                    <text v-if="item.expandList.length < item.hui_count && item.expandList.length != 0">展开更多回复</text>
                                    <text v-if="item.expandList.length >= item.hui_count">收起</text>
                                    <text
                                        class="cicon-back"
                                        :style="'transform: rotate(' + (item.expandList.length >= item.hui_count ? 90 : 270) + 'deg);font-size: 30rpx;'"
                                    ></text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view :class="'cu-load ' + (!di_get_reply ? 'loading' : 'over')"></view>
                </scroll-view>
                <view class="cu-bar search bg-white solid-top" style="position: absolute; bottom: 50rpx; width: 100%; padding-top: 20rpx">
                    <view class="search-form round" style="height: 80rpx; background-color: #f3f3f4">
                        <input
                            maxlength="100"
                            :value="ReplyText"
                            @input="check_text"
                            @confirm="send_confirm"
                            type="text"
                            :focus="focus"
                            :placeholder="placeholder"
                            confirm-type="send"
                            style="text-align: left; padding-left: 30rpx; height: 80rpx; line-height: 80rpx"
                        />
                        <text v-if="input_col" @tap.stop.prevent="none_focus" class="cicon-close-round text-gray" style="font-size: 25px; padding-right: 10rpx"></text>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal ' + (payMode ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">解锁剧集</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <!-- <view style="text-align: center;padding: 30rpx;font-size: 40rpx;font-weight: 600;">本集需要支付</view> -->
                <view class="text-center">
                    <view style="margin: 30rpx 0rpx">
                        <text class="text-price"></text>
                        <text style="font-size: 80rpx; color: #009933">{{ payInfo.paid_unlocking_price }}</text>
                        <text v-if="payInfo.paid_unlocking_type == 2">（{{ payInfo.confer }}）</text>
                        <text style="font-size: 24rpx; margin-left: 10rpx" v-if="payInfo.paid_unlocking_type == 1">（{{ payInfo.currency }}）</text>
                    </view>
                    <view style="font-size: 28rpx; margin-top: 40rpx; color: #333333">
                        <text>我的：</text>
                        <text v-if="payInfo.paid_unlocking_type == 1">{{ payInfo.user_conch }}{{ payInfo.currency }}</text>
                        <text v-if="payInfo.paid_unlocking_type == 2">{{ payInfo.user_fraction }}{{ payInfo.confer }}</text>
                    </view>
                    <view style="padding: 50rpx 20rpx">
                        <button v-if="payInfo.ads == 1" @tap="adShow" class="cu-btn round bg-blue" style="width: 280rpx; height: 80rpx">看一段广告解锁</button>
                        <button v-if="payInfo.ads == 0" @tap="hideModal" class="cu-btn round" style="width: 250rpx; height: 80rpx">暂不支付</button>
                        <button @tap="pay" class="cu-btn round bg-green" style="width: 250rpx; margin-left: 50rpx; height: 80rpx">立即支付</button>
                    </view>
                </view>
            </view>
        </view>

        <view :class="'cu-modal ' + (BindPhone ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">微信手机号授权</view>
                </view>
                <view class="bg-white" style="padding: 0rpx 50rpx 50rpx 50rpx">
                    <view style="font-size: 12px" class="text-grey">根据《网络安全法》相关规定，请绑定手机号</view>
                    <view class="padding flex flex-direction" style="margin-top: 20rpx">
                        <button style="height: 80rpx" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber" class="cu-btn round bg-black" @tap="hideModal">
                            微信手机号一键绑定
                        </button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
import http from '../../../util/http.js';
var rewardedVideoAd = null;
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            u: {},
            select: false,
            initial_time: 0,
            animBack: {},
            current: 0,
            // 记录上一个current
            circular: false,
            // 是否可以循环播放
            isPlaying: true,
            // 暂停|播放
            videoList: [],
            // 页面的视频列表
            videoInfo: [],
            windowH: 0,
            rzindex: 0,
            id: 0,
            epis: 0,
            info: {},
            epis: [],
            backRateIndex: 0,
            backRate: [1, 1.25, 1.5, 2],
            di_get_reply: false,
            modalName: false,
            placeholder: '评论',
            RrplyListCount: 0,
            ReplyText: '',
            reply_page: 1,
            RrplyList: [],
            input_col: false,
            key: 0,
            reply_id: 0,
            expand_page: 1,
            payMode: false,
            payInfo: {},
            swiperIndex: 0,
            lock: 0,
            index: 0,
            index_epis: 0,
            fullscreen: 'vertical',
            controls: false
        };
    },
    onReady() {
        this.videoContext = uni.createVideoContext('myVideo', this);
        console.log(this.videoContext);
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.id = options.id;
        this.index = options.index;
        this.index_epis = options.epis;
        this.doIt();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage(d) {
        var info = this.info;
        return {
            title: info.title,
            path: '/yl_welore/pages/packageC/theatre_video/index?id=' + info.id + '&index=' + this.swiperIndex + '&epis=' + this.videoList[this.swiperIndex].id,
            imageUrl: info.poster_url
        };
    },
    /**
     * 用户点击右上角分享
     */
    onShareTimeline() {
        var info = this.info;
        return {
            title: info.title,
            path: '/yl_welore/pages/packageC/theatre_video/index?id=' + info.id + '&index=' + this.swiperIndex + '&epis=' + this.videoList[this.swiperIndex].id,
            imageUrl: info.poster_url
        };
    },

    methods: {
        tou() {
            this.videoContext.startCasting();
            this.isPlaying = false;
            this.videoContext.pause();
        },
        ping() {
            this.modalName = true;
        },
        showPay() {
            this.payMode = true;
        },
        pay() {
            var that = this;
            uni.showModal({
                title: '提示',
                content: '确定支付吗？',
                confirmText: '支付',
                success: function (res) {
                    if (res.confirm) {
                        that.payDo();
                    }
                }
            });
        },
        payDo() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.epis = this.epis;
            var b = app.globalData.api_root + 'Microseries/pay_do';
            http.POST(b, {
                params: params,
                success: function (res) {
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: '解锁成功！',
                            icon: 'none',
                            duration: 1500
                        });
                        that.videoList[that.swiperIndex].msi_episode_url = res.data.info;
                        that.lock = 0;
                        that.payMode = false;
                        that.playVideo();
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: function (res) {}
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        open_bs() {
            console.log(this.backRateIndex);
            if (this.backRateIndex >= 3) {
                this.backRateIndex = 0;
            } else {
                this.backRateIndex = this.backRateIndex + 1;
            }
            this.videoContext.playbackRate(Number(this.backRate[this.backRateIndex]));
        },
        quan() {
            this.controls = true;
            this.videoContext.requestFullScreen();
        },
        no_quan() {
            this.controls = false;
            this.videoContext.exitFullScreen();
        },
        quanOp(d) {
            console.log(d);
            var key = d.detail.direction;
            if (key == 'vertical') {
                this.controls = false;
            } else {
                this.controls = true;
            }
            this.fullscreen = key;
        },

        async doIt() {
            var e = app.globalData.getCache('userinfo');
            const do0 = await this.checkToken();
            if (!e || do0.data.status == 'no') {
                //缓存为空执行登陆
                const do1 = await this.getLogin();
            } else {
                //字段为空执行登陆
                if (typeof e.token_impede == 'undefined') {
                    const do1 = await this.getLogin();
                } else {
                    //字段0或者小于当前时间执行登陆
                    var t = parseInt(+new Date() / 1000);
                    if (e.token_impede == 0 || t >= e.token_impede) {
                        const do1 = await this.getLogin();
                    } else {
                        this.u = e;
                    }
                }
            }
            const do4 = await this.getVinfo();
        },
        /**
         * 登陆
         */
        getLogin() {
            return new Promise((resolve, reject) => {
                uni.login({
                    success(res) {
                        console.log(res);
                        var params = new Object();
                        params.code = res.code;
                        http.POST(app.globalData.api_root + 'Login/index', {
                            params: params,
                            success: function (open) {
                                console.log(open);
                                if (open.data['code'] != 0) {
                                    uni.showModal({
                                        title: '系统提示',
                                        content: '您设置的小程序参数有误，请自行检查！'
                                    });
                                    return;
                                }
                                var data = new Object();
                                data.openid = open.data.info.openid;
                                data.session_key = open.data.info.session_key;
                                console.log(data);
                                http.POST(app.globalData.api_root + 'Login/add_tourist', {
                                    params: data,
                                    success: function (d) {
                                        console.log(d.data.info);
                                        app.globalData.setCache('userinfo', d.data.info);
                                        resolve(d);
                                    }
                                });
                            }
                        });
                    }
                });
            });
        },
        checkToken() {
            return new Promise((resolve, reject) => {
                var e = app.globalData.getCache('userinfo');
                if (!e) {
                    resolve(e);
                    return 'no'; //执行登陆
                } else {
                    var b = app.globalData.api_root + 'Check/check_token';
                    var that = this;
                    var e = app.globalData.getCache('userinfo');
                    var params = new Object();
                    params.token = e.token;
                    params.openid = e.openid;
                    http.POST(b, {
                        params: params,
                        success: function (res) {
                            resolve(res);
                        },
                        fail: function () {
                            uni.showModal({
                                title: '提示',
                                content: '网络繁忙，请稍候重试！',
                                showCancel: false,
                                success: function (res) {}
                            });
                        }
                    });
                }
            });
        },
        like(d) {
            var like_type = d.currentTarget.dataset.key;
            var index = d.currentTarget.dataset.index;
            var is_xh = this.videoList[index].is_xh;
            var is_zj = this.videoList[index].is_zj;
            if (like_type == 0) {
                //0 喜欢 1追剧
                if (is_xh > 0) {
                    this.videoList[index].is_xh = 0;
                } else {
                    this.videoList[index].is_xh = 1;
                }
            } else {
                if (is_zj > 0) {
                    this.videoList[index].is_zj = 0;
                } else {
                    this.videoList[index].is_zj = 1;
                }
            }
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.epis = this.epis;
            params.like_type = like_type;
            var b = app.globalData.api_root + 'Microseries/like_do';
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        AddReply(d) {
            var index = d.currentTarget.dataset.key;
            var id = d.currentTarget.dataset.id;
            var info = this.RrplyList[index];
            this.input_col = true;
            this.key = 1;
            this.reply_id = id;
            this.focus = true;
            this.placeholder = '回复 ' + info.user_nick_name;
        },
        VideoReply() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.page = this.reply_page;
            params.epis = this.epis;
            var RrplyList = that.RrplyList;
            var b = app.globalData.api_root + 'Microseries/get_video_reply';
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.status == 'success') {
                        if (res.data.huifu.length == 0 || res.data.huifu.length < 5) {
                            that.di_get_reply = true;
                        }
                        RrplyList = [...RrplyList, ...res.data.huifu];
                        that.RrplyList = RrplyList;
                        that.RrplyListCount = res.data.huifu_count;
                    } else {
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        none_focus() {
            this.input_col = false;
            this.ReplyText = '';
            this.key = 0;
            this.reply_id = 0;
            this.focus = false;
            this.placeholder = '评论';
        },
        combination(d) {
            console.log(d);
            var index = d.currentTarget.dataset.key;
            var id = d.currentTarget.dataset.id;
            var info = this.RrplyList[index];
            console.log(info);
            if (info.expandList.length == 0) {
                this.expand_reply_list(id, index);
            }
            if (info.expandList.length < info.hui_count && info.expandList.length != 0) {
                this.expand_page = this.expand_page + 1;
                this.expand_reply_list(id, index);
            }
            if (info.expandList.length >= info.hui_count) {
                this.expand_page = 1;
                this.RrplyList[index].expandList = [];
            }
        },
        expand_reply_list(id, index) {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id;
            params.page = this.expand_page;
            var expandList = that.RrplyList[index]['expandList'];
            var b = app.globalData.api_root + 'Microseries/get_expand_list';
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    expandList = [...expandList, ...res.data];
                    that.RrplyList[index].expandList = expandList;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        check_text(e) {
            console.log(e);
            var v = e.detail.value;
            if (v == '') {
                this.input_col = false;
                this.ReplyText = '';
            } else {
                this.input_col = true;
                this.ReplyText = v;
            }
        },
        send_confirm(d) {
            this.ReplyText = d.detail.value;
            this.ReplyDo();
        },
        ReplyDo() {
            uni.showLoading({
                title: '回复中...',
                mask: true
            });
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.msc_id = this.epis;
            params.reply_id = this.reply_id;
            params.text = this.ReplyText;
            console.log(params);
            var b = app.globalData.api_root + 'Microseries/reply_do';
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.hideLoading();
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        that.VideoReply();
                    } else if (res.data.status == 'error') {
                        uni.hideLoading();
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg
                        });
                    } else {
                        uni.hideLoading();
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg
                        });
                    }
                    that.ReplyText = '';
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        getV(epis, swiperIndex) {
            var that = this;
            var b = app.globalData.api_root + 'Microseries/video';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.openid = e.openid;
            params.epis = epis;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    that.payInfo = res.data.pay;
                    that.current = swiperIndex;
                    that.swiperIndex = swiperIndex;
                    that.epis = that.videoList[swiperIndex].id;
                    that.isPlaying = true;
                    that.backRateIndex = 0;
                    that.lock = res.data.lock;

                    if (res.data.lock == -1) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: function (res) {}
                        });
                    } else if (res.data.lock == 0 || res.data.lock == 3) {
                        that.videoList[swiperIndex].msi_episode_url = res.data.info;
                        that.$forceUpdate(); // force re-render
                        that.playVideo();
                        if (res.data.lock == 3) {
                            uni.showModal({
                                title: '提示',
                                content: '系统赠送您一次免费观看次数！',
                                showCancel: false,
                                success: function (res) {}
                            });
                        }
                    } else if (res.data.lock == 1) {
                        //会员专享
                        uni.showModal({
                            title: '提示',
                            content: '本集会员专享，请开通会员后重试！',
                            cancelText: '暂不',
                            confirmText: '去开通',
                            success: function (res) {
                                if (res.confirm) {
                                    uni.navigateTo({
                                        url: '/yl_welore/pages/packageB/user_vip/index'
                                    });
                                } else if (res.cancel) {
                                    console.log('用户点击取消');
                                }
                            }
                        });
                    } else if (res.data.lock == 2) {
                        //需要付费专享
                        that.payMode = true;
                    }
                    that.VideoReply();
                    //that.adShow();
                },

                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        adShow() {
            var that = this;
            uni.showLoading({
                title: '广告加载中...',
                mask: true
            });
            if (uni.createRewardedVideoAd) {
                rewardedVideoAd = uni.createRewardedVideoAd({
                    adUnitId: that.payInfo.incentive_id
                });
                rewardedVideoAd.onLoad(() => {
                    console.log('onLoad event emit');
                });
                rewardedVideoAd.onError((err) => {
                    console.log('onError event emit', err);
                    uni.showModal({
                        title: '提示',
                        showCancel: false,
                        content: '准备广告中，请稍后重试'
                    });
                });
                rewardedVideoAd.onClose((res) => {
                    console.log(res);
                    if (res && res.isEnded) {
                        console.log(1);
                        that.insAdLock();
                    } else {
                        that.payMode = true;
                    }
                });
                that.adDo();
            }
        },
        insAdLock() {
            var that = this;
            var b = app.globalData.api_root + 'Microseries/insAdLock';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.openid = e.openid;
            params.id = this.id;
            params.epis = this.epis;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: '解锁成功！',
                            icon: 'none',
                            duration: 1500
                        });
                        that.videoList[that.swiperIndex].msi_episode_url = res.data.info;
                        that.lock = 0;
                        that.playVideo();
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: function (res) {}
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        adDo() {
            this.payMode = false;
            rewardedVideoAd.show().catch(() => {
                rewardedVideoAd
                    .load()
                    .then(() => {
                        uni.hideLoading();
                        rewardedVideoAd.show();
                    })
                    .catch((err) => {
                        uni.showModal({
                            title: '提示',
                            showCancel: false,
                            content: '准备广告中，请稍后重试'
                        });
                    });
            });
        },
        getVinfo() {
            return new Promise((resolve, reject) => {
                var that = this;
                var b = app.globalData.api_root + 'Microseries/getInfo';
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.openid = e.openid;
                params.id = this.id;
                http.POST(b, {
                    params: params,
                    success: function (res) {
                        console.log(res);
                        if (res.data.status == 'error') {
                            uni.showModal({
                                title: '提示',
                                content: res.data.msg,
                                showCancel: false,
                                success: function (res) {}
                            });
                        } else {
                            that.info = res.data.info;
                            that.videoList = res.data.list;
                            that.epis = res.data.list[0].id;
                            var msi = app.globalData.getCache('msi-' + that.id);
                            if (msi) {
                                that.getV(msi.epis, msi.index);
                            } else {
                                if (typeof that.index != 'undefined') {
                                    that.getV(that.index_epis, that.index);
                                } else {
                                    that.getV(res.data.list[0].id, 0);
                                }
                            }
                        }
                        resolve(res);
                    },
                    fail: function () {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: function (res) {}
                        });
                    }
                });
            });
        },
        openEpis(d) {
            console.log(d);
            var id = d.currentTarget.dataset.id;
            var index = d.currentTarget.dataset.index;
            this.getV(id, index);
        },
        endNo() {
            this.fullscreen = 'vertical';
            this.no_quan();
            var info = this.videoList[this.swiperIndex + 1];
            var index = this.swiperIndex + 1;
            if (info) {
                var id = info.id;
                this.getV(id, index);
            }
        },
        tabSelect(e) {
            this.TabCur = e.currentTarget.dataset.id;
            this.scrollLeft = (e.currentTarget.dataset.id - 1) * 60;
        },
        /**
         * 切换视频
         */
        changeSwiper: function (e) {
            //this.videoContext.pause()
            if (e.detail.source == 'touch') {
                // 手动
                let data = this;
                // swiper滑到的位置
                let swiperIndex = e.detail.current;
                // 当前item的位置
                let current = data.current;
                if (current == swiperIndex) {
                    // wx.showToast({
                    //     title: '最后一集啦~',
                    //     icon: 'none',
                    //     duration: 2000
                    // })
                    return;
                }
                this.swiperIndex = swiperIndex;
                this.getV(this.videoList[swiperIndex].id, swiperIndex);
            }
        },
        formatSecondsToMS(roundedSeconds) {
            let rounded = Math.round(roundedSeconds);
            let minutes = Math.floor(rounded / 60);
            let seconds = rounded % 60;
            minutes = ('0' + minutes).slice(-2);
            seconds = ('0' + seconds).slice(-2);
            return `${minutes}:${seconds}`;
        },
        /**
         * 切换视频后 播放视频
         */
        playVideo: function (e) {
            this.videoContext.play();
        },
        playOrPausedd() {
            console.log(123);
        },
        /**
         * 手动 暂停|播放
         */
        playOrPause: function (e) {
            if (this.fullscreen != 'vertical') {
                return;
            }
            console.log(this.fullscreen);
            console.log(this.isPlaying);
            let data = this;
            if (data.isPlaying) {
                this.videoContext.pause();
            } else {
                this.videoContext.play();
            }
            let isPlayingTemp = !data.isPlaying;
            this.isPlaying = isPlayingTemp;
        },
        /**
         * 开始播放
         */
        eventPlay(e) {
            // console.log("-========>>>>>>  eventPlay 开始播放")
            this.rzindex = -1;
        },
        /**
         * 播放进度变化
         */
        timeupdate(e) {
            // console.log("-========>>>>>>  timeupdate 时间")
            let percent = (e.detail.currentTime / e.detail.duration) * 100;
            // console.log(e)
            this.percent = percent;
            app.globalData.setCache('msi-' + this.id, {
                epis: this.epis,
                index: this.swiperIndex,
                time: e.detail.currentTime,
                addTime: Math.floor(Date.now() / 1000)
            });
        },
        /**
         * 视频缓冲
         */
        videoWaiting(e) {
            // console.log(e)
            //console.log("-========>>>>>>  videoWaiting")
        },
        /**
         * 加载元数据
         */
        loadedmetadata(e) {
            // console.log("-========>>>>>>  loadedmetadata")
        },
        /**
         * 加载进度变化时触发，只支持一段加载
         */
        progress(e) {
            //console.log("-========>>>>>>  progress")
            //console.log(e.detail.buffered)
        },
        open_mode() {
            var animBackCollect = uni.createAnimation({
                duration: 200,
                timingFunction: 'linear'
            });
            animBackCollect.height('50%').step();
            this.animBack = animBackCollect.export();
            this.select = true;
        },
        hideModalPl() {
            this.modalName = false;
            this.focus = false;
            this.placeholder = '评论';
        },
        hideModal() {
            var animBackCollect = uni.createAnimation({
                duration: 200,
                timingFunction: 'linear'
            });
            animBackCollect.height('92%').step();
            this.animBack = animBackCollect.export();
            this.select = false;
            this.payMode = false;
        },
        open_url(d) {
            console.log(d);
            var type = d.currentTarget.dataset.type;
            if (type == 1) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/theatre_my/index'
                });
            }
        },
        openInfo() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageC/theatre_my_series/index?id=' + this.id
            });
        }
    }
};
</script>
<style>
page {
    height: 100%;
    background-color: #000000;
}
swiper {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
}
swiper-item {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

swiper-item .action {
    position: absolute;
    bottom: 480rpx;
    right: 24rpx;
}

swiper-item .info {
    font-size: 14px;
    color: #ffffff;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: auto;
    padding: 24rpx 0rpx 24rpx 24rpx;
    box-sizing: border-box;
    background-color: transparent; /* 浏览器不支持时显示 */
    background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.3));
}

.content {
    font-size: 28rpx;
    margin-bottom: 18rpx;
}

.checkBtn {
    width: 50%;
    height: 68rpx;
    line-height: 68rpx;
    text-align: center;
    font-size: 24rpx;
    border-radius: 8rpx;
    border: 1px solid #ffffff;
    margin: 0 auto;
}

swiper video {
    width: 100%;
    height: 92%;
    position: fixed;
    top: 0;
}

.pause {
    width: 65rpx;
    height: 65rpx;
    position: absolute;
    top: calc(50% - 100rpx);
    left: calc(50% - 35rpx);
    bottom: calc(50% - 30px);
    z-index: 99999999999;
}
.cu-list.menu > .cu-item.arrow:before {
    color: #672917;
}
.progress-box {
    position: fixed;
    bottom: 130rpx;
    width: 100%;
    z-index: 10;
}
.tt view {
    margin-top: 20rpx;
}

.jj {
    text-align: center;
    line-height: 100rpx;
    width: 100rpx;
    height: 100rpx;
    background-color: #dddddd;
    border-radius: 20rpx;
    color: #ffffff;
    font-weight: 600;
    margin: 10rpx 20rpx;
    position: relative;
}
.ss {
    text-align: center;
    line-height: 100rpx;
    width: 100rpx;
    height: 100rpx;
    background-color: #ffcc33;
    border-radius: 20rpx;
    color: #000000;
    font-weight: 600;
    margin: 10rpx 20rpx;
    position: relative;
}

button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
}

button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
    overflow: inherit;
}
</style>
