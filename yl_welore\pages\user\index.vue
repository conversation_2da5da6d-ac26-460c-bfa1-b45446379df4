<template>
  <view>
    <Index1 v-if="mod.user == '0'" :parentData="currentInstance" @my_home="my_home" @my_level="my_level"
      @onGotUserInfo="onGotUserInfo" @bid_qiandao="bid_qiandao" @user_url="user_url"></Index1>
    <Index2 v-if="mod.user == 'b325e6a8-59a1-e68f-e1dd-0e83c71f78b5'" :parentData="currentInstance" @my_home="my_home"
      @my_level="my_level" @onGotUserInfo="onGotUserInfo" @bid_qiandao="bid_qiandao" @user_url="user_url"></Index2>
    <Index3 v-if="mod.user == '18d6e3a8-3fb2-ef77-6f91-95efed73672a'" :parentData="currentInstance" @my_home="my_home"
      @my_level="my_level" @onGotUserInfo="onGotUserInfo" @bid_qiandao="bid_qiandao" @user_url="user_url"></Index3>
    <Index4 v-if="mod.user == '11c36fb0-1103-111b-4047-69e8df5a0d49'" :parentData="currentInstance" @my_home="my_home"
      @my_level="my_level" @onGotUserInfo="onGotUserInfo" @bid_qiandao="bid_qiandao" @user_url="user_url"></Index4>
    <tabbar id="tabbar" :tabbar="tabbar" :activeIndex="4"></tabbar>
    <phone id="phone" @close_phone_modal="check_phone = false;" :check_phone="check_phone"></phone>

    <view :class="'cu-modal ' + (Skymodal ? 'show' : '')">
      <view class="cu-dialog">
        <view class="cu-bar bg-white justify-end">
          <view class="content">设置昵称</view>
          <!-- <view class="action" @tap="hideModal">
            <text class="cuIcon-close text-red"></text>
          </view> -->
        </view>
        <form @submit="formSubmit">
          <view class="padding-xl">
            <view style="width: 80px;height: 80px;margin: 0 auto;position: relative;">
              <button open-type="chooseAvatar" @chooseavatar="onChooseAvatar" hover-class="none"
                style="position: relative;">
                <image v-if="(!avatarUrl)" :src="(http_root) + 'addons/yl_welore/web/static/applet_icon/default.png'"
                  style="width: 80px;height: 80px;"></image>
                <image v-if="avatarUrl" :src="avatarUrl" style="width: 80px;height: 80px;border-radius: 50%;"></image>

                <image src="/static/yl_welore/style/icon/xiangji.png"
                  style="width: 46rpx;height:46rpx;position: absolute;right: 0;bottom: 0;">
                </image>
              </button>
            </view>
            <view class="cu-form-group margin-top-xl">
              <view class="title">昵称</view>
              <input name="nickname" type="nickname" placeholder="起个漂亮的名字~" />
            </view>
            <view class="padding flex flex-direction margin-top-lg">
              <button form-type="submit" class="cu-btn bg-yellow text-white margin-tb-sm lg round">设置</button>
            </view>
          </view>
        </form>
      </view>
    </view>
  </view>
</template>

<script>
import tabbar from "@/yl_welore/util/tabbarComponent/modern-tabbar";
import phone from "@/yl_welore/util/user_phone/phone";
import Index1 from "@/yl_welore/pages/user/index1.vue";
import Index2 from "@/yl_welore/pages/user/index2.vue";
import Index3 from "@/yl_welore/pages/user/index3.vue";
import Index4 from "@/yl_welore/pages/user/index4.vue";
var app = getApp();
var http = require("../../util/http.js");
export default {
  components: {
    tabbar,
    phone,
    Index1,
    Index2,
    Index3,
    Index4
  },
  /**
   * 页面的初始数据
   */
  data() {
    return {
      currentInstance: this, // 通过data属性中转 
      http_root: app.globalData.http_root,
      tabbar: {},
      design: {},
      user_current: 'user',
      copyright: {},
      user_info: {},
      animationData: null,
      animationDataD: null,
      animation_qian: null,
      animation_qian_yes: null,
      flag: false,
      diy: {},
      isPopping: false,
      //是否已经弹出
      animPlus: {},
      //旋转动画
      animCollect1: {},
      animCollect: {},
      //item位移,透明度
      animTranspond: {},
      //item位移,透明度
      animInput: {},
      //item位移,透明
      animBack: {},
      version: 1,
      admin: 0,
      mod: '',
      ios: false,
      avatarUrl: '',
      Skymodal: false,
      originalHeight: 0,
      originalWidth: 0,
      check_phone: false,
      open_cord: false,
      open_wangpan: false,
      open_account: false,
      open_lost: false,
      open_used: false,
      open_employ: false,
      open_convenience: false,
      open_sweepstake: false,
      elect_sheathe: ''
    };
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) { },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    uni.hideTabBar();
    var phone = app.globalData.__PlugUnitScreen('57713e6253e2753382c4de447a44c543');
    var wangpan = app.globalData.__PlugUnitScreen('eae4f6cbbecdb89ea5a61ec602ec7000');
    var account = app.globalData.__PlugUnitScreen('67673460afb6b32dbd74b65f7d42b56a');
    var lost = app.globalData.__PlugUnitScreen('27bc52a3b4dcfd099671fb09706f02d8');
    var convenience = app.globalData.__PlugUnitScreen('937c07a443d278405c670892d2fc89b6');
    var used = app.globalData.__PlugUnitScreen('a11eb9c1955977a6d890dca4991209f6');
    var employ = app.globalData.__PlugUnitScreen('7d6353619854f00edf2d1e9a44430333');
    var open_sweepstake = app.globalData.__PlugUnitScreen('7e1d82d91e04523ae2825c1d2991d5d6');
    this.open_employ = employ;
    this.open_used = used;
    this.open_convenience = convenience;
    this.open_cord = phone;
    this.open_wangpan = wangpan;
    this.open_account = account;
    this.open_lost = lost;
    this.open_sweepstake = open_sweepstake;
    var dd = uni.getStorageSync('is_diy');
    console.log(dd);
    if (dd) {
      this.design = dd;
      this.mod = dd.mod;
      this.elect_sheathe = dd.elect_sheathe;
      app.globalData.editTabbar();
    } else {
      this.get_diy();
    }
    var copyright = getApp().globalData.store.getState().copyright;
    if (copyright) {
      this.copyright = copyright;
    } else {
      this.authority();
    }
    this.get_user_info();
    var that = this;
    //获取系统信息
    uni.getSystemInfo({
      success(res) {
        var copyright = getApp().globalData.store.getState().copyright;
        console.log(res.platform);
        if (res.platform == "ios" && copyright.ios_pay_arbor == 0) {
          that.ios = false;
        }
        if (res.platform == "ios" && copyright.ios_pay_arbor == 1) {
          that.ios = true;
        }
        if (res.platform != "ios") {
          that.ios = true;
        }
      }
    });
    var tips = app.globalData.getCache("Tips");
    var this_time = parseInt(+new Date() / 1000);
    if (tips > this_time) {
      app.globalData.store.setState({
        slogin: 1
      });
    } else {
      app.globalData.store.setState({
        slogin: 0
      });
    }
  },
  methods: {
    my_level() {
      var that = this;
      uni.navigateTo({
        url: '/yl_welore/pages/packageD/user_levle/index'
      });
    },
    my_home() {
      var that = this;
      var e = that.user_info;
      console.log('11111');
      console.log(e);
      if (e.tourist == 0) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/my_home/index?id=' + this.user_info.id
        });
      }
    },
    hideModal() {
      this.Skymodal = false;
    },
    formSubmit(d) {
      console.log(d);
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.openid = e.openid;
      params.nike_name = d.detail.value.nickname;
      params.avatarUrl = this.avatarUrl;
      if (this.avatarUrl == '') {
        uni.showToast({
          title: '上传个头像吧~',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      params.much_id = app.globalData.siteInfo.uniacid;
      http.POST(app.globalData.api_root + 'Login/new_do_login', {
        params: params,
        success: (res) => {
          console.log(res);
          uni.showModal({
            title: '提示',
            content: res.data.msg,
            showCancel: false,
            success: (res) => { }
          });
          if (res.data.code == 0) {
            var user_info = res.data.info;
            console.log(user_info);
            app.globalData.setCache("userinfo", user_info);
            that.get_user_info();
            that.Skymodal = false;
          }
        },
        fail: () => { }
      });
    },
    onChooseAvatar(d) {
      var that = this;
      var avatarUrl = d.detail.avatarUrl;
      var e = app.globalData.getCache("userinfo");
      uni.uploadFile({
        url: app.globalData.api_root + 'User/img_upload',
        filePath: avatarUrl,
        name: 'sngpic',
        header: {
          "content-type": "multipart/form-data"
        },
        formData: {
          "content-type": "multipart/form-data",
          'token': e.token,
          'openid': e.openid,
          'much_id': app.globalData.siteInfo.uniacid
        },
        success: (res) => {
          console.log(res);
          var data = JSON.parse(res.data);
          console.log(data);
          uni.showToast({
            title: data.msg,
            icon: 'none',
            duration: 2000
          });
          if (data.status == 'success' || data.code == 1) {
            that.avatarUrl = data.url;
          }
        },
        fail: (res) => {
          uni.showToast({
            title: res.data.msg,
            icon: '上传错误！',
            duration: 2000
          });
        }
      });
    },
    onGotUserInfo(q) {
      this.check_phone = true;
    },
    get_diy() {
      var b = app.globalData.api_root + 'User/get_diy';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          that.mod = res.data.mod;
          that.elect_sheathe = res.data.elect_sheathe;
          that.design = res.data;
          uni.setStorageSync("is_diy", res.data);
          app.globalData.editTabbar();
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    user_url(d) {
      console.log(d);
      var index = d.currentTarget.dataset.index;
      var e = app.globalData.getCache("userinfo");
      if (e.tourist == 1) {
        uni.showToast({
          title: '请登录！',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      if (index == 1) {
        if (this.ios == true) {
          if (e.user_phone) {
            uni.navigateTo({
              url: '/yl_welore/pages/packageB/user_vip/index'
            });
          } else {
            this.check_phone = true;
          }
          return;
        }
      }
      if (index == 2) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/user_follow/index?id=' + e.uid + '&type=1'
        });
      }
      if (index == 3) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/user_follow/index?id=' + e.uid + '&type=2'
        });
      }
      if (index == 5) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageC/user_collection/index'
        });
      }
      if (index == 6) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/circle_master/index'
        });
      }
      if (index == 7) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/gift_received/index'
        });
      }
      if (index == 8) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageA/user_order/index'
        });
      }
      if (index == 9) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageC/user_details/index'
        });
      }
      if (index == 10) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageC/user_invitation/index'
        });
      }
      if (index == 11) {
        uni.navigateTo({
          url: '/yl_welore/pages/shell_mall/index'
        });
      }
      if (index == 12) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageC/service_centre/index'
        });
      }
      if (index == 13) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageC/examine/index'
        });
      }
      if (index == 14) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageC/about_us/index'
        });
      }
      if (index == 15) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/black_list/index'
        });
      }
      if (index == 16) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/card_bag/index'
        });
      }
      if (index == 17) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageB/card_check/index'
        });
      }
      if (index == 18) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageD/user_task/index'
        });
      }
      if (index == 19) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageD/user_welfare/index'
        });
      }
      if (index == 20) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageD/whisper/index'
        });
      }
      if (index == 21) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageD/nameplate/index'
        });
      }
      if (index == 22) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageE/certification_list/index'
        });
      }
      if (index == 23) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageE/notes/index'
        });
      }
      if (index == 24) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageD/user_medal/index'
        });
      }
      if (index == 25) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageF/cammy/index'
        });
      }
      if (index == 26) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageF/netdisc/index'
        });
      }
      if (index == 27) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageF/account/index'
        });
      }
      if (index == 28) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageF/lost_list/index'
        });
      }
      if (index == 29) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageE/convenience_info/index'
        });
      }
      if (index == 30) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageE/easy_take/index'
        });
      }
      if (index == 31) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageE/used_goods_list/index'
        });
      }
      if (index == 32) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageE/employment_list/index'
        });
      }
      if (index == 33) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageD/draw_list/index'
        });
      }
      if (index == 34) {
        uni.navigateTo({
          url: '/yl_welore/pages/packageC/theatre/index'
        });
      }
    },
    show_qian() {
      let that = this;
      console.log("animate");
      var animation = uni.createAnimation({
        duration: 750,
        timingFunction: 'ease'
      });
      this.animation = animation;
      animation.opacity(1).step();
      that.animationData = animation.export();
    },
    bid_qiandao() {
      var e = app.globalData.getCache("userinfo");
      if (e.tourist == 1) {
        uni.showToast({
          title: '请登录！',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      uni.vibrateShort();
      let that = this;
      console.log("animate");
      var animation = uni.createAnimation({
        duration: 750,
        timingFunction: 'ease'
      });
      var animation_q = uni.createAnimation({
        duration: 750,
        timingFunction: 'ease'
      });
      this.animation_q = animation_q;
      animation_q.opacity(0).step();
      that.animation_qian = animation_q.export();
      this.animation = animation;
      animation.opacity(0).step();
      that.animationData = animation.export();
      setTimeout(() => {
        that.user_punch();
      }, 400);
    },
    bid_qiandao_d() {
      let that = this;
      var info = that.user_info;
      info.is_sign = 1;
      that.user_info = info;
      var animation = uni.createAnimation({
        duration: 200,
        timingFunction: 'linear'
      });
      var animation_yes = uni.createAnimation({
        duration: 200,
        timingFunction: 'linear'
      });
      this.animation_yes = animation_yes;
      animation_yes.opacity(0).step();
      that.animation_qian_yes = animation_yes.export();
      this.animation = animation;
      animation.opacity(1).step();
      that.animationDataD = animation.export();
      setTimeout(() => {
        animation_yes.translateX(-100).opacity(1).step();
        that.animation_qian_yes = animation_yes.export();
        animation.opacity(1).scale(0.4).step();
        that.animationDataD = animation.export();
      }, 200);
    },
    /**
     * 签到
     */
    user_punch() {
      var b = app.globalData.api_root + 'User/add_user_punch';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      params.uid = e.uid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
            that.bid_qiandao_d();
          } else {
            that.show_qian();
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    /**
     * 信息站点
     */
    authority() {
      var b = app.globalData.api_root + 'User/get_authority';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: (res) => {
          that.copyright = res.data;
          app.globalData.store.setState({
            copyright: res.data
          });
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    },
    //获取用户信息
    get_user_info() {
      var b = app.globalData.api_root + 'User/get_user_info';
      var that = this;
      var e = app.globalData.getCache("userinfo");
      var params = new Object();
      params.token = e.token;
      params.openid = e.openid;
      http.POST(b, {
        params: params,
        success: (res) => {
          console.log(res);
          if (res.data.status == 'success') {
            that.version = res.data.info.version;
            that.user_info = res.data.info;
            that.admin = res.data.info.admin;
            that.flag = res.data.info.is_sign == 1 ? true : false;
            if (res.data.info.status == 0) {
              uni.navigateTo({
                url: '/yl_welore/pages/black_house/index'
              });
              return;
            }
          } else {
            uni.showToast({
              title: res.data.msg,
              icon: 'none',
              duration: 2000
            });
          }
        },
        fail: () => {
          uni.showModal({
            title: '提示',
            content: '网络繁忙，请稍候重试！',
            showCancel: false,
            success: (res) => { }
          });
        }
      });
    }
  },
  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    var forward = app.globalData.forward;
    console.log(forward);
    if (forward) {
      return {
        title: forward.title,
        path: '/yl_welore/pages/index/index',
        imageUrl: forward.reis_img
      };
    } else {
      return {
        title: '您的好友给您发了一条信息',
        path: '/yl_welore/pages/index/index'
      };
    }
  }
};
</script>
<style>
.placeholder_login {
  text-align: center;
  height: 6em;
  line-height: 2.3em;
}

.cu-tag {
  font-weight: 700;
}

.win_phone {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}

.win_phone_text {
  color: #FFFFFF;
  position: absolute;
  bottom: 0;
  padding: 5px;
  letter-spacing: 1px;
  font-weight: 500;
}

.vip_style {
  width: 90%;
  height: 45px;
  background-image: linear-gradient(to right, #262626 0%, #444444 70%);
  margin: 15px auto;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.vip_style_2 {
  width: 100%;
  height: 45px;
  background-image: linear-gradient(to top, #e6b980 0%, #eacda3 100%);
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}

.placeholder {
  margin: 15px;
  text-align: center;
}

page {
  background: #f9f9f9;
}

.demo-row {
  text-align: center;
  margin-top: 20rpx;
}

.number_text {
  font-weight: 700;
  font-size: 20px;
  height: 38px;
}

.info_text {
  font-size: 12px;
  color: #000;
}

.back-pre {
  width: 32rpx;
  height: 32rpx;
  margin-top: 11rpx;
  margin-left: 0rpx;
}


button::after {
  line-height: normal;
  font-size: 30rpx;
  width: 0;
  height: 0;
  top: 0;
  left: 0;
}

button {
  line-height: normal;
  display: block;
  padding-left: 0px;
  padding-right: 0px;
  background-color: rgba(255, 255, 255, 0);
  font-size: 30rpx;
}

.img-style {
  height: 0rpx;
  width: 0rpx;
  position: absolute;
  right: 50%;
  opacity: 0;
  bottom: 0px;
}

.weui-tabbar_boo {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  position: fixed;
  z-index: 1;
  bottom: 0;
  width: 100%;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
}

.weui-tabbar_boo_no {
  /* display: -webkit-box;
  display: -webkit-flex; */
  position: fixed;
  z-index: 1;
  bottom: 23%;
  right: 0;
  /* width: 100%; */
}

.text_one {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal !important;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.container {
  /* background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab); */
  background-image: radial-gradient(circle 248px at center, #16d9e3 0%, #30c7ec 47%, #46aef7 100%);
  background-size: 300% 300%;
  animation: gradientBG 10s ease infinite;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.center_san {
  font-weight: 300;
  margin-top: 5px;
  font-size: 14px;
}

.center_text_cu {
  height: 54rpx;
  font-weight: 700;
  font-size: 18px;
}

.left_color {
  color: #AFAFAF;
  font-weight: 300;
  margin-top: 8px;
  font-size: 13px;
}

.di_img {
  width: 70rpx;
  height: 70rpx;
}
</style>