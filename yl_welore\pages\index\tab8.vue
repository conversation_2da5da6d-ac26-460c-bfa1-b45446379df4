<template>
    <view>
        <view v-if="sw_info.length > 0" style="margin-top: 10px">
            <swiper
                class="square-dot"
                :indicator-dots="true"
                :circular="true"
                :autoplay="true"
                interval="5000"
                duration="500"
                indicator-active-color="#ffffff"
                :style="'width:95%;margin:0 auto;height:' + imgheights[top_index] + 'rpx;'"
            >
                <block v-for="(item, sw_index) in sw_info" :key="sw_index">
                    <swiper-item style="border-radius: 5px">
                        <image
                            @tap.stop.prevent="open_navigator"
                            :data-src="item.playbill_url"
                            :data-path="item.wx_app_url"
                            :data-type="item.practice_type"
                            :data-url="item.url"
                            style="width: 100%; border-radius: 5px"
                            mode="widthFix"
                            :src="item.playbill_url"
                        />
                    </swiper-item>
                </block>
            </swiper>
        </view>
        <Home @set_one="set_one" @top_home_url="top_url" @get_all_qq="get_all_qq" @nex_my_qq="nex_my_qq" @this_url="this_url" :data="parentData"></Home>
        <view class="" style="margin: 0px 0px 20px 20px">
            <view @tap="handleChange" data-key="tab1" style="margin-left: 10px; line-height: 25px; position: relative; height: 30px; width: 70px; float: left">
                <text :class="current == 'tab1' ? '_this' : ''" style="position: absolute; z-index: 1">首页</text>
                <view
                    v-if="current == 'tab1'"
                    style="background-image: linear-gradient(120deg, #f6d365 0%, #fda085 100%); height: 18rpx; border-radius: 10px; width: 81rpx; position: absolute; bottom: 14rpx"
                ></view>
            </view>
            <view @tap="handleChange" data-key="tab3" style="margin-left: 10px; position: relative; line-height: 25px; position: relative; height: 30px; width: 70px; float: left">
                <text :class="current == 'tab3' ? '_this' : ''" style="position: absolute; z-index: 1">推荐</text>
                <view
                    v-if="current == 'tab3'"
                    style="background-image: linear-gradient(120deg, #f6d365 0%, #fda085 100%); height: 18rpx; border-radius: 10px; width: 81rpx; position: absolute; bottom: 14rpx"
                ></view>
            </view>
            <view @tap="handleChange" data-key="tab2" style="margin-left: 10px; position: relative; line-height: 25px; position: relative; height: 30px; width: 70px; float: left">
                <text :class="current == 'tab2' ? '_this' : ''" style="position: absolute; z-index: 1">关注</text>
                <view
                    v-if="current == 'tab2'"
                    style="background-image: linear-gradient(120deg, #f6d365 0%, #fda085 100%); height: 18rpx; border-radius: 10px; width: 81rpx; position: absolute; bottom: 14rpx"
                ></view>
            </view>
            <!-- <view bindtap="handleChange" data-key='tab4' style='margin-left:10px;position:relative;line-height:25px;position:relative;height:30px;width:70px;float:left;'>
  <text class="{{current=='tab4'?'_this':''}}" style='position:absolute;'>视频</text>
  <view wx:if="{{current=='tab4'}}" style='width:72%;height:10px;background-color:#FFBB00;margin-top:18px;border-radius:10px;'></view>
</view> -->
        </view>
        <view style="clear: both; height: 0"></view>
        <!-- 置顶 -->
        <view class="radius shadow-warp bg-white margin-top" style="margin: 10px; border-radius: 5px">
            <view class="weui-cell" style="padding: 10px 15px 10px 20px" v-for="(item, index) in home_list" :key="index">
                <navigator :url="'/yl_welore/pages/packageA/article/index?id=' + item.id + '&type=' + item.study_type" hover-class="none">
                    <view class="weui-cell__bd">
                        <text style="font-weight: 500; vertical-align: sub; float: left; margin-right: 10px; font-size: 14px; color: #ff4e4e">置顶</text>
                        <view class="text-cut" style="margin-left: 10px; font-size: 14px">
                            <text @tap="gambit_list" style="color: #0099ff; margin-right: 5px">{{ item.gambit_name }}</text>
                            {{ item.study_title == '' ? item.study_content : item.study_title }}
                        </view>
                    </view>
                </navigator>
            </view>
        </view>
        <!-- 置顶 -->
        <view style="clear: both; height: 0"></view>
    </view>
</template>

<script>
import Home from './home.vue';
export default {
    components: {
        Home 
    },
    props: ['data', 'compName'],
    computed: {
        parentData() {
            return this.$parent.$data;
        },
        sw_info() {
            return this.$parent.$data.sw_info;
        },
        cardCur() {
            return this.$parent.$data.cardCur;
        },
        current() {
            return this.$parent.$data.current;
        },
        home_list() {
            return this.$parent.$data.home_list;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        
    },
    methods: {
        bindchange_top(e) {
            this.$emit('bindchange_top', e);
        },
        open_navigator(e) {
            this.$emit('open_navigator', e);
        },
        handleChange(e) {
            this.$emit('handleChange', e);
        },
        gambit_list(e) {
            this.$emit('gambit_list', e);
        },
        cardSwiper(e) {
            this.$emit('cardSwiper', e);
        },
        set_one(e) {
            this.$emit('set_one', e);
        },
        top_url(e) {
            this.$emit('top_url', e);
        },
        get_all_qq(e) {
            this.$emit('get_all_qq', e);
        },
        nex_my_qq(e) {
            this.$emit('nex_my_qq', e);
        },
        this_url(e) {
            this.$emit('this_url', e);
        }
    }
};
</script>
<style></style>
